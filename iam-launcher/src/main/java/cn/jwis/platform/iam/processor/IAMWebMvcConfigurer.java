package cn.jwis.platform.iam.processor;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Component
public class IAMWebMvcConfigurer implements WebMvcConfigurer {

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix("iam-config-server", (clazz) -> clazz.getName().startsWith("cn.jwis.platform.iam" +
                ".config"));
        configurer.addPathPrefix("iam-foundation-server", (clazz) -> clazz.getName().startsWith("cn.jwis.platform.plm" +
                ".foundation"));
        configurer.addPathPrefix("iam-file-server", (clazz) -> clazz.getName().startsWith("cn.jwis.platform.plm.file"));
    }
}
