package cn.jwis.platform.iam;


import cn.jwis.framework.base.annotation.EnableJWIScannerSupporter;
import cn.jwis.framework.configration.ConfigCenterHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@ComponentScan(basePackages = {"cn.jwis"})
@SpringBootApplication
@EnableTransactionManagement
@EnableFeignClients("cn.jwis")
@EnableJWIScannerSupporter
@EnableScheduling
@EnableAsync
public class IAMApplication {

    public static final String SERVICE_NAME = "iam-server";

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(IAMApplication.class);
        Map<String, Object> defaultMap = new HashMap<>();
        // 调用sdk包内读取配置的方法
        ConfigCenterHelper.getConfig(defaultMap, SERVICE_NAME);
        // 将配置加载到启动项中
        springApplication.setDefaultProperties(defaultMap);
        springApplication.run(args);
    }

    private static final Logger logger = LoggerFactory.getLogger(IAMApplication.class);

    @Autowired
    private ConfigurableEnvironment springEnv;

    @PostConstruct
    public void print(){
        MutablePropertySources propSrcs = springEnv.getPropertySources();
        StringBuilder stringBuilder = new StringBuilder();
        propSrcs.stream().filter(ps -> ps instanceof EnumerablePropertySource)
                .flatMap(it -> Arrays.stream(((EnumerablePropertySource<?>) it).getPropertyNames()))
                .forEach(proName -> {
                    if("server.port".equals(proName))
                        stringBuilder.append("\n\n");
                    String value = (value = springEnv.getProperty(proName)) == null ? "" : value;
                    stringBuilder.append(proName + "=" + new String(value.getBytes(StandardCharsets.UTF_8)) + "\n");
                });

        logger.info("props\n" + stringBuilder.toString() + "\n");
    }

}
