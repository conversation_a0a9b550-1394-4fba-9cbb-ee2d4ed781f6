#Config Micro-Service Port
server.port=8112
server.servlet.context-path=/iam-server-ych
spring.main.allow-bean-definition-overriding=true
spring.servlet.multipart.max-file-size=2048MB
spring.servlet.multipart.max-request-size=2048MB
#Config Logging Export
logging.level.org.apache.ibatis=debug
logging.file=logs/log.log
log4j.logger.org.apache.ibatis=INFO
log4j.logger.java.sql=INFO 
spring.mvc.static-path-pattern=/**
spring.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,\
  classpath:/static/,classpath:/public/
web.date_farmat=yyyy/MM/dd HH:mm:ss
database.storage.type=neo4j
#neo4j
spring.data.neo4j.uri=bolt://***********:7687
spring.data.neo4j.username=neo4j
spring.data.neo4j.password=neo4j123
spring.data.neo4j.database=digitaliam
spring.data.neo4j.default.database=digitaliam
# redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.database=21
spring.default.redis.database=21
spring.redis.password=redis
spring.redis.pool.max-idle=8
spring.redis.pool.max-wait=-1
spring.redis.pool.min-idle=0
spring.redis.pool.max-active=8
spring.redis.connect.cluster=false
saas.redis.index.start=100
saas.redis.saas.storage=2
# \u7B2C\u4E09\u65B9\u8C03\u7528
file.service.gateway.url=http://***********:5003/iam-server-ych/iam-file-server
#\u63A5\u53E3\u514D\u5BC6
jwis.platform.iam.service.gateway.url=http://***********:5003
#Foundation
layout.default.init.code=create,update,show
#\u5BA1\u8BA1
audit.framework.open=false
# MinIO
minio.endpoint=***********
minio.port=9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.secure=false
minio.default.bucket=mybucket
minio.bucket.max.capacity=20
minio.bucket.startCreateStrategy=false

#mq\u914D\u7F6E\u4FE1\u606F\u53CA\u6570\u636E\u53D1\u653E\u53C2\u6570
data.distribution.open=true
msgqueue.service.access.uri=http://***********:14004/msgqueue
mq.mqmessageserviceoid=acf7d978-d994-4fce-89ca-9a49ef8735bb
mq.vhname=digital-pdm-test
event.domain=iam
# notification
email.switch=on
email.emailHost=smtp.jwis.cn
email.smtp_auth=true
email.ssl_enable=true
email.user=<EMAIL>
email.password=PDM2022@SYS
email.encode=utf-8
email.protocol=smtp
#\u7528\u6237\u670D\u52A1
#\u8BBE\u7F6Ecookie\u3001token \u767B\u5F55\u7684\u6709\u6548\u671F\u5355\u4F4D \u5929
login.cookie.timeout.day=120
spring.messages.basename=i18n/account
#\u5BC6\u7801\u7B56\u7565\u5F00\u5173 on:\u5F00\u542F/off:\u5173\u95ED
login.check.password.policy.switch=on
iam.login.user.account=sys_admin,security_admin
feign.compression.request.min-request-size=4096
layout.default.init.code=create,update,show
#\u662F\u5426\u5F00\u542F\u7F13\u5B58\u67E5\u8BE2
open.redis.cache=false


#PPM\u540C\u6B65\u63A5\u53E3\u57DF\u540D
ppm.sync.url=http://iamgateway-rdbms.dev.jwis.cn/ppm-standalone/account-micro
#\u4F01\u4E1A\u5FAE\u4FE1
worKwx.api.url=https://qyapi.weixin.qq.com/cgi-bin
work.wx.corp.id=wwb2bf375e9e2555a2
work.wx.corp.secret=PXDZC29alusKCSx0rpnqipJolJIRu0TxTqFszRXIH8k
#\u6FC0\u6D3B\u7684ppm\u79DF\u6237oid
ppm.tenant.oid.default=2890e298-762b-4b28-b2c9-2b0224fd6c7c
ppm.tenant.alias.default=cagbjhiijc

#AD\u57DF\u96C6\u6210
#adDomain.userName=ldap
#adDomain.password=Yhht@2020
#adDomain.host=************
#adDomain.domain=@test
#adDomain.port=389
#adDomain.searchBase=OU=yinhe_all,DC=test,DC=local

adDomain.userName=opsadmin
adDomain.password=Yhht@2020
adDomain.host=***********
adDomain.domain=@yinhe
adDomain.port=389
adDomain.searchBase=OU=yinhe_all,DC=yinhe,DC=local

iam.local.user.account=sys_admin,security_admin,hll,pdm1,pdm2,zhanghuan,zwc,manager3906,pdm3,pdm4,pdm5