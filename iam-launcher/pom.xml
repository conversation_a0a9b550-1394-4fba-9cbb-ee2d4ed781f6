<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iam-server</artifactId>
        <groupId>cn.jwis.platform.iam</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0.0</version>

    <artifactId>iam-launcher</artifactId>

    <properties>
        <!-- properties 必须要有一个namespace,否则开发云运行会失败 -->
        <namespace></namespace>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>iam-web</artifactId>
        </dependency>

        <!--Foundation 基础服务-->
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>foundation-repo-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>foundation-web</artifactId>
        </dependency>

        <!-- DFS 分布式文件系统-->
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>file-repo-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>file-web</artifactId>
        </dependency>

        <!--iam config -->
        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>config-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>config-repo-neo4j</artifactId>
        </dependency>


    </dependencies>
    <profiles>
        <profile>
            <id>dev</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>3.1</version>
                        <configuration>
                            <source>1.8</source>
                            <target>1.8</target>
                            <encoding>UTF-8</encoding>
                            <!-- 此处配置打包时必须排除本地的配置文件，防止配置中心的配置信息失效-->
                            <excludes>
                                <!--                        <exclude>**/application*.properties</exclude>-->
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.5.4</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <resources>
                    <resource>
                        <directory>src/lib</directory>
                        <targetPath>/lib</targetPath>
                        <includes>
                            <include>**/*.jar</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                        <includes>
                            <!--                    开发环境为解开注释状态，部署需要注释掉，不include 配置文件-->
                            <include>*.properties</include>
                            <include>**/*.xml</include>
                            <include>**/*.yaml</include>
                        </includes>
                        <filtering>false</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>package</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>3.1</version>
                        <configuration>
                            <source>1.8</source>
                            <target>1.8</target>
                            <encoding>UTF-8</encoding>
                            <!-- 此处配置打包时必须排除本地的配置文件，防止配置中心的配置信息失效-->
                            <excludes>
                                <exclude>**/application*.properties</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.5.4</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <resources>
                    <resource>
                        <directory>src/lib</directory>
                        <targetPath>/lib</targetPath>
                        <includes>
                            <include>**/*.jar</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                        <includes>
                            <!--                    开发环境为解开注释状态，部署需要注释掉，不include 配置文件-->
<!--                            <include>*.properties</include>-->
                            <include>**/*.xml</include>
                            <include>**/*.yaml</include>
                        </includes>
                        <filtering>false</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>

</project>