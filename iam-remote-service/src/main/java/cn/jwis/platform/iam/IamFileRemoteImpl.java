package cn.jwis.platform.iam;

import feign.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： Created in 2022/11/3
 * @Description : 文件服务集成
 */
@Component
public class IamFileRemoteImpl implements IamFileRemote {

    @Resource
    IamFileRemoteFeign iamFileRemoteFeign;


    @Override
    public Response downloadByFileOid(String oid) {
        return iamFileRemoteFeign.downloadByOid(oid);
    }
}
