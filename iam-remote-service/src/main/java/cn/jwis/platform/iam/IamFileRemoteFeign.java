package cn.jwis.platform.iam;

import cn.jwis.framework.base.feign.FeignConfig;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "iamFileRemoteFeign", configuration = FeignConfig.class, url = "${file.service.gateway.url}")
public interface IamFileRemoteFeign {

    // 文件下载
    @RequestMapping(value = "/file/downloadByOid", method = RequestMethod.GET)
    Response downloadByOid(@RequestParam String fileOid);

}
