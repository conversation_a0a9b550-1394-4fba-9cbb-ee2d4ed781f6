<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="lastExternalPluginCheckTime" value="1751512564980" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5pYW0tcmVtb3RlLXNlcnZpY2U8L2lkPjxjbGFzc3BhdGg+PGRpciBuYW1lPSIvVXNlcnMvY29ybi95aGh0L2lhbS9jb2RlL2lhbS1zZXJ2ZXIvaWFtLXJlbW90ZS1zZXJ2aWNlL3RhcmdldC9jbGFzc2VzIj48L2Rpcj48L2NsYXNzcGF0aD48L2FwcGxpY2F0aW9uPg==" />
          </map>
        </option>
        <option name="version" value="9" />
      </configuration>
    </facet>
  </component>
</module>