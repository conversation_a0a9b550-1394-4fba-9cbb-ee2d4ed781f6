package cn.jwis.platform.iam.auth.helper;


import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.platform.iam.auth.dto.AuthDTO;
import cn.jwis.platform.iam.auth.dto.LoginDTO;
import cn.jwis.platform.iam.auth.dto.TokenDTO;
import cn.jwis.platform.iam.user.User;

public interface AuthHelper {

    AuthDTO login(String appName, LoginDTO loginDTO, String ip) throws JWIException;

    AuthDTO logout(String account, boolean isMobile) throws JWIException;

    String generateJsonWebToken(User user) throws JWIException;

    void unlockAccount(String account) throws JWIException;

    TokenDTO accessToken(String token) throws JWIException;
}
