package cn.jwis.platform.iam.account.dto;

import cn.jwis.platform.plm.foundation.attachment.entity.File;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/18
 * @Description :账号编辑
 */
@Data
public class AccountUpdateDTO {

    private String oid;
    @ApiModelProperty("用户名称")
    private String name;
    @ApiModelProperty("电话")
    private String phone;
    @ApiModelProperty("邮箱地址")
    private String email;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("头像")
    private String avatar;
    @ApiModelProperty("签名文件")
    private File signature;
    @ApiModelProperty("关联成员")
    private String personnelOid;
}
