package cn.jwis.platform.iam.product.helper;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.i18n.I18nUtils;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.DateUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.iam.IamFileRemote;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.common.help.MakeTreeHelper;
import cn.jwis.platform.iam.constants.ActionConstants;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.helper.CompanyHelper;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.personnel.response.PersonnelWithAccessInfo;
import cn.jwis.platform.iam.personnel.response.ProductWithAccessInfo;
import cn.jwis.platform.iam.product.ProductService;
import cn.jwis.platform.iam.product.dto.AccessDTO;
import cn.jwis.platform.iam.product.dto.AccessMultipleProductDTO;
import cn.jwis.platform.iam.product.dto.AccessUpdateDTO;
import cn.jwis.platform.iam.product.dto.FuzzProductCategoryDTO;
import cn.jwis.platform.iam.product.dto.FuzzyByProductOidDTO;
import cn.jwis.platform.iam.product.dto.LicenseInfoDTO;
import cn.jwis.platform.iam.product.dto.ProductCategoryCreateDTO;
import cn.jwis.platform.iam.product.dto.ProductCategoryUpdateDTO;
import cn.jwis.platform.iam.product.dto.ProductCreateDTO;
import cn.jwis.platform.iam.product.dto.ProductPageByPersonnelDTO;
import cn.jwis.platform.iam.product.dto.ProductUpdateDTO;
import cn.jwis.platform.iam.product.dto.SubscribeDTO;
import cn.jwis.platform.iam.product.entity.LicenseText;
import cn.jwis.platform.iam.product.entity.Product;
import cn.jwis.platform.iam.product.entity.ProductCategory;
import cn.jwis.platform.iam.product.entity.ProductLicense;
import cn.jwis.platform.iam.product.response.ProductMemberNum;
import cn.jwis.platform.iam.relation.AccessTo;
import cn.jwis.platform.iam.relation.LinkTo;
import cn.jwis.platform.iam.response.AccessInfoWithPath;
import cn.jwis.platform.iam.response.ProductAndLicenseInfo;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.structure.TreeNode;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import com.google.common.collect.Sets;
import feign.Response;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class ProductHelperImpl implements ProductHelper {

    protected static final Class<Product> POJO_CLASS = Product.class;

    private static final Logger logger = LoggerFactory.getLogger(ProductHelperImpl.class);

    @Value(value = "${open.redis.cache:false}")
    private boolean openCache;

    @Resource
    CommonService commonService;

    @Resource
    ProductService productService;

    @Resource
    IamFileRemote iamFileRemote;

    @Resource
    CommonAbilityHelper commonAbilityHelper;

    @Resource
    CompanyHelper companyHelper;

    @Resource
    PersonnelHelper personnelHelper;

    @Resource
    MakeTreeHelper makeTreeHelper;

    @Resource
    AccessCacheHelper accessCacheHelper;

    @Override
    @RestLock
    public Product create(ProductCreateDTO dto) {
        ValidationUtil.validate(dto);
        Assert.isNull(findByName(dto.getName()), "common.name.repeat");
        Assert.isNull(findByProductKey(dto.getProductKey()), "product.key.repeat");

        if(StringUtils.isNotBlank(dto.getCategoryOid())) {
            ProductCategory productCategory = commonService.findByOid(ProductCategory.TYPE,dto.getCategoryOid(),
                    ProductCategory.class);
            Assert.notNull(productCategory, "product.category.not.exits");
        }

        String licenseContent = transformResponseInput2String(dto.getLicenseOid());
        String publicKey = transformResponseInput2String(dto.getPublicKeyOid());
        LicenseText licenseText = productService.getLicenseText(licenseContent, publicKey);

        ProductLicense productLicense = new ProductLicense(dto.getLicenseOid(), dto.getPublicKeyOid(),
                licenseText.getActiveDate(),
                licenseText.getExpirationDate(), licenseText.getMemberNumAllow(), licenseText.getProductNumber());

        Assert.isFalse(!checkLicenseValid(productLicense), String.format(Locale.ENGLISH, "%s:%s",
                I18nUtils.getMessage("product.license.expires"), productLicense.getProductKey()));

        Product product = BeanUtil.copyProperties(dto, new Product());
        if (StringUtils.isBlank(dto.getCategoryOid())) {
            product.setCategoryOid(StringUtils.EMPTY);
        }

        product = commonAbilityHelper.doCreate(product);
        productLicense.setOid(OidGenerator.newOid());
        commonService.create(productLicense);
        commonService.createRelation(new LinkTo(ProductLicense.TYPE, productLicense.getOid(), Product.TYPE,
                product.getOid()));
        return product;
    }

    @Override
    public Product update(ProductUpdateDTO dto) {
        ValidationUtil.validate(dto);
        Product product = commonService.findByOid(Product.TYPE, dto.getOid(), Product.class);
        if (!StringUtils.equals(dto.getName(), product.getName())) {
            Product sameName = findByName(dto.getName());
            Assert.isFalse(!ObjectUtils.isEmpty(sameName) && !StringUtils.equals(product.getOid(), sameName.getOid()),
                    "common.name.repeat");
        }

        if(StringUtils.isNotBlank(dto.getLicenseOid())) {
            ProductLicense productLicense = getProductLicense(product);
            String publicKeyOid =  StringUtils.isNotBlank(dto.getPublicKeyOid()) ? dto.getPublicKeyOid() :
                    productLicense.getPublicKeyOid();

            String licenseContent = transformResponseInput2String(dto.getLicenseOid());
            String publicKey = transformResponseInput2String(publicKeyOid);

            LicenseText licenseText = productService.getLicenseText(licenseContent, publicKey);
            Assert.isTrue(licenseText.getProductNumber().equals(product.getProductKey()), "product.key.diff");

            int accessNum = productService.queryAccessPersonnelOidList(product.getOid()).size();
            Assert.isFalse(accessNum > licenseText.getMemberNumAllow(), String.format(Locale.ENGLISH, I18nUtils.getMessage("product.license.compare.full"), licenseText.getMemberNumAllow(), accessNum));
            productLicense.setLicenseOid(dto.getLicenseOid());
            productLicense.setPublicKeyOid(publicKeyOid);
            productLicense.setActiveDate(licenseText.getActiveDate());
            productLicense.setExpirationDate(licenseText.getExpirationDate());
            productLicense.setMemberNumAllow(licenseText.getMemberNumAllow());

            Assert.isFalse(!checkLicenseValid(productLicense), String.format(Locale.ENGLISH,
                    "%s:%s", I18nUtils.getMessage("product.license.expires"), productLicense.getProductKey()));
            commonService.update(productLicense);
        }

        BeanUtil.copyProperties(dto, product);
        return commonAbilityHelper.doUpdate(product);
    }

    @Override
    public Long delete(String oid) {
        Product product = commonService.findByOid(Product.TYPE, oid, Product.class);
        Assert.notNull(product, "product not exists");

        Assert.isFalse(productService.productHasAccessTo(product.getOid()), "product.has.bind");
        ProductLicense productLicense = getProductLicense(product);
        commonService.delete(ProductLicense.TYPE, productLicense.getOid());
        return commonService.delete(Product.TYPE,product.getOid());
    }

    @RestLock
    @Override
    public boolean createMultiple(AccessMultipleProductDTO dto) {
        ValidationUtil.validate(dto);
        Personnel personnel = commonService.findByOid(Personnel.TYPE,dto.getPersonnelOid(),Personnel.class);

        Assert.notNull(personnel, "personnel.not.exits");
        Assert.isFalse(personnel.isInvalidFlag(), "personnel.is.invalid");
        Assert.notEmpty(personnel.getAssociatedAccount(), "personnel.account.blank");
        // 检查应用清单是否有效
        List<Product> productList = queryAndChekOidValid(Product.TYPE, dto.getProductOidList(),
                Product.class,
                Product::getOid);

        productList.forEach(item -> {
            getProductLicense(item, true);
        });

        List<String> toAccessList = productList.stream().map(Product::getOid).collect(Collectors.toList());

        List<String> hasAccessProduct =
                productService.queryPersonnelAccessProductList(personnel.getOid()).stream().map(Product::getOid).collect(Collectors.toList());

        toAccessList.removeAll(hasAccessProduct);

        if (CollectionUtils.isNotEmpty(toAccessList)) {
            List<ProductMemberNum> productMemberNums = productService.queryProductMemberInfo(toAccessList);
            List<AccessTo> accessToList = productMemberNums.stream().map(item->{
                Assert.isFalse(item.getMemberNumAllow() <= item.getMemberNum(),
                        String.format(Locale.ENGLISH, "%s : %s",I18nUtils.getMessage("product.license.full"),
                                item.getProductName()));

                long nowTime = System.currentTimeMillis();
                long activeDate = nowTime >= item.getActiveDate() ? nowTime : item.getActiveDate();
                AccessTo accessTo = new AccessTo(Personnel.TYPE, personnel.getOid(), ProductLicense.TYPE, item.getLicenseOid());
                accessTo.setActiveDate(activeDate);
                return accessTo;
            }).collect(Collectors.toList());
            commonService.createOutRelation(accessToList);

            List<Product> toNoticeProductList =
                    productList.stream().filter(item -> toAccessList.contains(item.getOid())).collect(Collectors.toList());

            toNoticeProductList.forEach(item -> {
                accessCacheHelper.addPersonnelAndRefreshCache(Sets.newHashSet(personnel.getOid()), item.getProductKey(),
                        ActionConstants.ACTION_GRANT);
            });
        }

        return true;
    }

    @Override
    public String analysisLicenseInfo(LicenseInfoDTO dto) {
        String licenseContent = transformResponseInput2String(dto.getLicenseOid());
        String publicKey = transformResponseInput2String(dto.getPublicKeyOid());
        return productService.getLicenseText(licenseContent, publicKey).getProductNumber();
    }

    @Override
    @RestLock
    public boolean toSubscribe(SubscribeDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), " product.oid.not.black");
        Assert.notEmpty(dto.getDepartmentOidList(), "department.oid.not.black");
        // 检查应用是否有效
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");
        // 查询授权license
        ProductLicense productLicense = getProductLicense(product, true);
        // 检查部门清单是否有效
        List<Department> departmentList = queryAndChekOidValid(Department.TYPE, dto.getDepartmentOidList(),
                Department.class,
                Department::getOid);

        long nowTime = System.currentTimeMillis();
        long activeDate = nowTime >= productLicense.getActiveDate() ? nowTime : productLicense.getActiveDate();

        FromToFilter hasDepartmentFilter = new FromToFilter();
        hasDepartmentFilter.setFromType(Department.TYPE);
        hasDepartmentFilter.setType(AccessTo.TYPE);
        hasDepartmentFilter.setToType(ProductLicense.TYPE);
        hasDepartmentFilter.setToFilter(Condition.where("oid").eq(productLicense.getOid()));
        Set<String> hasDepartmentOidSet =
                commonService.dynamicQueryFrom(hasDepartmentFilter, Department.class).stream().map(Department::getOid).collect(Collectors.toSet());
        logger.info("toSubscribe department size {} , product oid {}", departmentList.size(), product.getOid());
        List<Department> toAccessList =
                departmentList.stream().filter(item -> !hasDepartmentOidSet.contains(item.getOid())).collect(Collectors.toList());
        logger.info("toSubscribe after filter department size {}  , product oid {}", toAccessList.size(),
                product.getOid());

        if (CollectionUtils.isEmpty(toAccessList)) {
            return true;
        }
        //部门清单下所有需要授权人员
        Set<String> needToAccessSet =
                personnelHelper.queryByOrgList(departmentList.stream().map(Department::getOid).collect(Collectors.toList()),
                        false, true).stream().collect(Collectors.toSet());
        // 将部门下成员加入授权清单
        handlerAccessPersonnel(needToAccessSet, productLicense, product.getOid(), product.getProductKey());
        // 建立部门订阅
        List<AccessTo> accessToList = toAccessList.stream().map(item -> {
            AccessTo accessTo = new AccessTo(Department.TYPE,
                    item.getOid(), ProductLicense.TYPE, productLicense.getOid());
            // 创建时不设置过期时间
            accessTo.setActiveDate(activeDate);
            return accessTo;
        }).collect(Collectors.toList());
        commonService.createOutRelation(accessToList);

        return true;
    }

    private void handlerAccessPersonnel(Set<String> needToAccessSet, ProductLicense productLicense,
                                        String productOid,String productKey) {

        logger.info("toSubscribe personnel in department size {}  , product oid {}", needToAccessSet.size(),
                productOid);
        //已经授权人员
        Set<String> hasAccessSet =
                productService.queryAccessPersonnelOidList(productOid).stream().map(Personnel::getOid).collect(Collectors.toSet());
        logger.info("toSubscribe has access personnel size {} , product oid {}", hasAccessSet.size(), productOid);
        // 取人员差集
        needToAccessSet.removeAll(hasAccessSet);
        logger.info("toSubscribe need access personnel size {} , product oid {}", needToAccessSet.size(), productOid);
        // 校验剩余空间是否允许授权
        Assert.isTrue(productLicense.getMemberNumAllow() >= needToAccessSet.size() + hasAccessSet.size(),
                String.format(Locale.ENGLISH, I18nUtils.getMessage("product.license.add.full"), productLicense.getMemberNumAllow(), hasAccessSet.size(),
                        needToAccessSet.size()));

        long nowTime = System.currentTimeMillis();
        long activeDate = nowTime >= productLicense.getActiveDate() ? nowTime : productLicense.getActiveDate();

        List<AccessTo> accessToList = needToAccessSet.stream().map(item -> {
            AccessTo accessTo = new AccessTo(Personnel.TYPE,
                    item, ProductLicense.TYPE, productLicense.getOid());
            // 创建时不设置过期时间
            accessTo.setActiveDate(activeDate);
            return accessTo;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(accessToList)) {
            commonService.createOutRelation(accessToList);
        }
        // 部门订阅后添加人员进行缓存刷新
        if (CollectionUtils.isNotEmpty(needToAccessSet)) {
            accessCacheHelper.addPersonnelAndRefreshCache(needToAccessSet, productKey, ActionConstants.ACTION_GRANT);
        }
    }

    private <T> List<T> queryAndChekOidValid(String type, Set<String> oidList, Class<T> target, Function<? super T,
            String> mapper) {
        // 检查部门清单是否有效
        List<T> resultList = commonService.dynamicQuery(type,
                Condition.where("oid").in(oidList),
                target);

        Assert.notEmpty(resultList, "common.oid.error");
        List<String> invalidDepartmentList = findInvalidId(oidList,
                resultList.stream().map(mapper).collect(Collectors.toSet()));
        Assert.isEmpty(invalidDepartmentList, String.format(Locale.ENGLISH, " %s : %s",I18nUtils.getMessage("common" +
                        ".oid.error"), StringUtils.join(invalidDepartmentList, ",")));
        return resultList;
    }

    private ProductLicense getProductLicense(Product product, boolean withCheck) {
        FromToFilter licenseFilter = new FromToFilter();
        licenseFilter.setFromType(ProductLicense.TYPE);
        licenseFilter.setType(LinkTo.TYPE);
        licenseFilter.setToType(Product.TYPE);
        licenseFilter.setToFilter(Condition.where("oid").eq(product.getOid()));
        // 检查应用License信息是存在
        List<ProductLicense> productLicenseList = commonService.dynamicQueryFrom(licenseFilter, ProductLicense.class);
        Assert.notEmpty(productLicenseList, String.format(Locale.ENGLISH, " %s: %s",
                I18nUtils.getMessage("product.license.not.exits"), product.getProductKey()));
        ProductLicense productLicense = productLicenseList.stream().findFirst().get();
        Assert.isFalse(withCheck && !checkLicenseValid(productLicense), String.format(Locale.ENGLISH, "%s:%s",
                I18nUtils.getMessage("product.license.expiration"),
                productLicense.getProductKey()));
        return productLicense;
    }

    private ProductLicense getProductLicense(Product product) {
        return getProductLicense(product, false);
    }

    @Override
    @RestLock
    public ProductCategory createProductCategory(ProductCategoryCreateDTO dto) {
        ValidationUtil.validate(dto);
        ProductCategory productCategory = findCategoryByName(dto.getName());
        Assert.isTrue(ObjectUtils.isEmpty(productCategory),"common.name.repeat");
        productCategory = new ProductCategory();
        BeanUtil.copyProperties(dto,productCategory);
        productCategory.setOid(OidGenerator.newOid());
        return commonService.create(productCategory);
    }

    private ProductCategory findCategoryByName(String name) {
        return commonService.dynamicQueryOne(ProductCategory.TYPE,
                Condition.where("name").eq(name),
                ProductCategory.class);
    }

    @Override
    public ProductCategory updateProductCategory(ProductCategoryUpdateDTO dto) {
        ValidationUtil.validate(dto);
        ProductCategory productCategory = commonService.findByOid(ProductCategory.TYPE,dto.getOid(),
                ProductCategory.class);
        Assert.notNull(productCategory,"product.category.not.exits");
        ProductCategory sameNameCategory = findCategoryByName(dto.getName());
        Assert.isFalse(!ObjectUtils.isEmpty(sameNameCategory) && !sameNameCategory.getOid().equals(productCategory.getOid()),"Name be used");
        BeanUtil.copyProperties(dto,productCategory);
        return commonService.update(productCategory);
    }

    @Override
    public List<ProductCategory> queryCategory() {
        return commonService.dynamicQuery(ProductCategory.TYPE, null, new Order().desc("name"), ProductCategory.class);
    }

    @Override
    public Long deleteProductCategory(String categoryOid) {
        ProductCategory productCategory = commonService.findByOid(ProductCategory.TYPE,categoryOid,
                ProductCategory.class);
        Assert.notNull(productCategory,"product.category.not.exits");

        List<Product> productList = commonService.dynamicQuery(Product.TYPE,
                Condition.where("categoryOid").eq(categoryOid),Product.class);
        if(CollectionUtils.isNotEmpty(productList)) {
            productService.updateProductCategory(productList.stream().map(Product::getOid).collect(Collectors.toList()), StringUtils.EMPTY);
        }
        return commonService.delete(ProductCategory.TYPE,productCategory.getOid());
    }

    @Override
    public Product updateCategory(String productOid, String categoryOid) {
        Assert.notBlank(productOid, "product.oid.not.black");
        Product product = commonService.findByOid(Product.TYPE, productOid, Product.class);
        Assert.notNull(product, "product.not.exits");
        if (StringUtils.isBlank(categoryOid)) {
            product.setCategoryOid(StringUtils.EMPTY);
        } else {
            ProductCategory productCategory = commonService.findByOid(ProductCategory.TYPE, categoryOid,
                    ProductCategory.class);
            Assert.notNull(product, "product.category.not.exits");
            product.setCategoryOid(productCategory.getOid());
        }
        return commonService.update(product);
    }

    @Override
    public ProductLicense getProductLicense(String productOid) {
        Product product = commonService.findByOid(Product.TYPE, productOid, Product.class);
        Assert.notNull(product, "product.not.exits");
        return getProductLicense(product);
    }

    @Override
    public Long updateProductCategory(List<String> productOidList, String categoryOid) {
        return productService.updateProductCategory(productOidList, categoryOid);
    }

    @Override
    public Map<String, Set<String>> queryAllAccessPersonnelGroupByProductKey() {
        return productService.queryAllAccessPersonnelGroupByProductKey();
    }

    @Override
    public Set<String> queryAccessPersonnelByProductKey(String productKey) {
        return null;
    }

    @Override
    public TreeNode queryTreeByProductKey(String productKey) {
        List<TreeAbleEx> orgList;
        List<? extends TreeAbleEx> perList;
        logger.info(" queryTreeByProductKey by productKey {} ", productKey);
        if(openCache) {
            Set<String> perSet = accessCacheHelper.getPerByProductKey(productKey);
            Set<String> orgSet = accessCacheHelper.getOrgByProductKey(productKey);
            if (CollectionUtils.isEmpty(perSet)) {
                logger.info(" queryTreeByProductKey by personnelList is empty");
                return new TreeNode();
            }
            logger.info(" queryTreeByProductKey perSet size {} , orgSet size {} ", perSet.size(), orgSet.size());
            orgList =
                    companyHelper.getCurrentIdOrgMap().entrySet().stream().filter(item -> orgSet.contains(item.getKey())).map(item -> item.getValue()).collect(Collectors.toList());
            // 授权应用下人员查询 将人员替换为用户信息
            perList = personnelHelper.queryUserWithPositionByPersonnelOidList(perSet.stream().collect(Collectors.toList()));
        } else {
            Product product = findByProductKey(productKey);
            Assert.notNull(product,"Product not exists");
            ProductLicense productLicense = getProductLicense(product);

            FromToFilter personnelFilter = new FromToFilter();
            personnelFilter.setFromType(Personnel.TYPE);
            personnelFilter.setType(AccessTo.TYPE);
            personnelFilter.setToType(ProductLicense.TYPE);
            personnelFilter.setToFilter(Condition.where("oid").eq(productLicense.getOid()));

            List<Personnel> personnelList = commonService.dynamicQueryFrom(personnelFilter,Personnel.class);

            logger.info(" queryTreeByProductKey perSet size {}", personnelList.size());
            if (CollectionUtils.isEmpty(personnelList)) {
                logger.info(" queryTreeByProductKey by personnelList is empty");
                return new TreeNode();
            }
            perList = personnelHelper.queryUserWithPositionByPersonnelOidList(personnelList.stream().map(Personnel::getOid).collect(Collectors.toList()));
            orgList = companyHelper.findLinkParentWithNodeList((List<TreeAbleEx>) perList);
            logger.info(" queryTreeByProductKey perSet size {} , orgSet size {} ", perList.size(), orgList.size());
        }
        orgList.addAll(perList);
        return makeTreeHelper.makeOrgTree(orgList);
    }

    @Override
    public List<Product> findSubscribeProductListByPositionOid(String positionOid) {
        return productService.findSubscribeProductListByPositionOid(positionOid);
    }

    @Override
    public boolean isExpire(long expirationDate) {
        return latterByDay(System.currentTimeMillis(), expirationDate);
    }

    private List<String> findInvalidId(Set<String> departmentOidList, Set<String> resultOidSet) {
        List<String> compareList = new ArrayList<>(departmentOidList);
        for (Iterator<String> iterator = resultOidSet.iterator(); iterator.hasNext(); ) {
            compareList.remove(iterator.next());
        }
        return compareList;
    }

    @Override
    public Long cancelSubscribe(SubscribeDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), " product.oid.not.black");
        // 此时为department 授权关系oid
        Assert.notEmpty(dto.getDepartmentOidList(), "department.oid.not.black");
        // 检查应用是否有效
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");

        FromToFilter filter = new FromToFilter();
        filter.setType(AccessTo.TYPE);
        filter.setRelFilter(Condition.where("oid").in(dto.getDepartmentOidList()));
        return commonService.deleteRelation(filter);
    }

    @RestLock
    @Override
    public boolean toAccess(AccessDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), " product.oid.not.black");
        // 检查应用是否有效
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");
        List<Personnel> personnelList = queryAndChekOidValid(Personnel.TYPE, dto.getPersonnelOidList(),
                Personnel.class,
                Personnel::getOid);
        //需要授权有账号人员
        Set<String> needToAccessSet =
                personnelList.stream().filter(item->StringUtils.isNotBlank(item.getAssociatedAccount()) && !item.isInvalidFlag()).map(Personnel::getOid).collect(Collectors.toSet());
        handlerAccessPersonnel(needToAccessSet, getProductLicense(product, true), product.getOid(),
                product.getProductKey());
        return true;
    }

    @Override
    public boolean cancelAccess(AccessDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), " product.oid.not.black");
        Assert.notEmpty(dto.getPersonnelOidList(), "personnel.oid.not.blank");
        // 检查应用是否有效
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");

        ProductLicense productLicense = getProductLicense(product);
        // 检查部门清单是否有效
        List<Personnel> personnelList = queryAndChekOidValid(Personnel.TYPE, dto.getPersonnelOidList(),
                Personnel.class,
                Personnel::getOid);

        FromToFilter filter = new FromToFilter();
        filter.setFromType(Personnel.TYPE);
        filter.setFromFilter(Condition.where("oid").in(personnelList.stream().map(Personnel::getOid).collect(Collectors.toList())));
        filter.setToType(ProductLicense.TYPE);
        filter.setToFilter(Condition.where("oid").eq(productLicense.getOid()));
        // 人员移除时进行缓存清除
        Long result = commonService.deleteRelation(filter);
        accessCacheHelper.removePersonnelAndRefreshCache(personnelList.stream().map(Personnel::getOid).collect(Collectors.toSet()),
                product.getProductKey());
        return result > 0;
    }

    @SneakyThrows
    @Override
    public boolean updateAccess(AccessUpdateDTO dto) {
        return updateAccessTo(dto);
    }

    @SneakyThrows
    private boolean updateAccessTo(AccessUpdateDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), "product.oid.not.black");
        Assert.notEmpty(dto.getOidList(), "common.oid.not.blank");
        Assert.isFalse(StringUtils.isBlank(dto.getActiveDate()) && StringUtils.isBlank(dto.getExpirationDate()),
                "Not data to update cancel");
        // 检查应用是否有效
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");

        List<AccessTo> accessToList = queryAccessToList(product.getOid());

        ProductLicense productLicense = getProductLicense(product, true);
        Set<String> oidSet = dto.getOidList().stream().collect(Collectors.toSet());

        List<AccessTo> accessesList = accessToList.stream().filter(item->oidSet.contains(item.getOid())).collect(Collectors.toList());

        Assert.isFalse(dto.getOidList().removeAll(accessesList.stream().map(AccessTo::getOid).collect(Collectors.toList())) && CollectionUtil.isNotEmpty(dto.getOidList()),
                String.format(Locale.ENGLISH, "%s %s:",I18nUtils.getMessage("common.oid.error"),
                        StringUtils.join(dto.getOidList().removeAll(accessesList), ",")));

        String activeDate = dto.getActiveDate();
        String expirationDate = dto.getExpirationDate();

        checkAndUpdateAccess(productLicense, accessesList, activeDate, expirationDate);
        return true;
    }

    private void checkAndUpdateAccess(ProductLicense productLicense, List<AccessTo> accessesList, String activeDate,
                                      String expirationDate) {
        Assert.notEmpty(accessesList.stream().filter(item -> !item.getOid().equals(productLicense.getOid())).collect(Collectors.toList()), "license is not match ");
        List<String> errorMessage = new ArrayList<>();
        Long activeTime = DateUtil.parseDate(activeDate).getTime();
        Long expirationTime = null;

        // 结束时间必须早于等于license的结束时间
        if (StringUtils.isNotBlank(expirationDate)) {
            if (!latterByDay(DateUtil.formatDate(new Date(productLicense.getExpirationDate())),
                    expirationDate)) {
                errorMessage.add("product.license.expires.error");
            } else {
                expirationTime = DateUtil.parseDate(expirationDate).getTime();
            }
        }
        // 开始时间必须大于等于当前开始时间
        for (AccessTo item : accessesList) {
            if (!latterByDay(activeDate, DateUtil.formatDate(new Date(item.getActiveDate())))) {
                errorMessage.add("product.license.active.error");
            }

            item.setActiveDate(activeTime);
            item.setExpirationDate(expirationTime);
        }

        Assert.isEmpty(errorMessage, String.format(Locale.ENGLISH, "%s: %s",I18nUtils.getMessage("product.date.error"),
                StringUtils.join(errorMessage, ",")));

        commonService.updateRelation(accessesList);
    }

    private boolean latterByDay(long source, long target) {
        return latterByDay(DateUtil.formatDate(new Date(source)), DateUtil.formatDate(new Date(target)));
    }

    private boolean latterByDay(String source, String target) {
        return source.compareTo(target) >= 0;
    }

    /**
     * 失效时间必须小于等于license失效时间
     *
     * @param productLicense license
     * @return 结果
     */
    private boolean checkLicenseValid(ProductLicense productLicense) {
        return latterByDay(productLicense.getExpirationDate(), System.currentTimeMillis());
    }

    @Override
    public PageResult<Product> fuzzyPage(FuzzProductCategoryDTO dto) {
        Condition condition = buildProductCondition(dto.getSearchKey(),dto.getCategoryOid());
        return commonService.dynamicQueryPage(Product.TYPE, condition, buildOrder(), dto.getIndex(),
                dto.getSize(), POJO_CLASS);
    }

    @Override
    public PageResult<AccessInfoWithPath> fuzzySubscribePage(FuzzyByProductOidDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), " product.oid.not.black");
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");

        PageResult<AccessTo> pageResult =
                productService.fuzzySubscribePage(product.getOid(),
                        buildAccessTargetCondition(dto.getSearchKey()), buildOrder(), dto.getIndex(),
                        dto.getSize());

        if (CollectionUtils.isEmpty(pageResult.getRows())) {
            return new PageResult<>(0, 0, dto.getSize(), Lists.newArrayList());
        }

        //查询当前部门oid以及 路径列表的 Map
        Map<String, LinkedList<String>> pathMap =
                makeTreeHelper.findNodePath(pageResult.getRows().stream().map(AccessTo::getFromOid).collect(Collectors.toSet()));
        List<AccessInfoWithPath> subscribeInfoList = pageResult.getRows().stream().map(item -> {
            LinkedList<String> pathList = pathMap.get(item.getFromOid());
            AccessInfoWithPath accessInfoWithPath = new AccessInfoWithPath();
            accessInfoWithPath.setOid(item.getOid());
            accessInfoWithPath.setExpirationDate(item.getExpirationDate() != null ?
                    DateUtil.formatDate(new Date(item.getExpirationDate())) : "");
            accessInfoWithPath.setActiveDate(DateUtil.formatDate(new Date(item.getActiveDate())));
            accessInfoWithPath.setDisplayName(pathList.getFirst());
            accessInfoWithPath.setPath(StringUtils.join(pathList, ">"));
            return accessInfoWithPath;
        }).collect(Collectors.toList());

        return new PageResult(pageResult.getCount(), pageResult.getPageIndex(), pageResult.getPageSize(),
                subscribeInfoList);
    }

    @Override
    public List<AccessTo> queryAccessToList(String productOid) {
        return productService.queryAccessToList(productOid);
    }

    @Override
    public PageResult<PersonnelWithAccessInfo> fuzzyAccessPage(FuzzyByProductOidDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notEmpty(dto.getProductOid(), " product.oid.not.black");
        Product product = commonService.findByOid(Product.TYPE, dto.getProductOid(), Product.class);
        Assert.notNull(product, "product.not.exits");

        return productService.fuzzyAccessPage(product.getOid(), buildAccessTargetCondition(dto.getSearchKey()),
                buildOrder(), dto.getIndex(), dto.getSize());
    }

    @Override
    public boolean updateSubscribe(AccessUpdateDTO accessUpdateDTO) {
        return updateAccessTo(accessUpdateDTO);
    }

    @Override
    public PageResult<ProductWithAccessInfo> queryAccessProductByPersonnel(ProductPageByPersonnelDTO dto) {
        return productService.queryAccessProductByPersonnel(dto.getPersonnelOid(), buildOrder(), dto.getIndex(),
                dto.getSize());
    }

    @Override
    public PageResult<Product> queryNoAccessProductByPersonnel(ProductPageByPersonnelDTO dto) {
        return productService.queryNoAccessProductByPersonnel(dto.getPersonnelOid(), buildOrder(), dto.getIndex(),
                dto.getSize());
    }

    @Override
    public TreeNode queryPersonnelTreeWithAccessFlag(String productOid,String searchKey) {
        return companyHelper.findOrgTreeWithAccessFlag(productService.queryAccessPersonnelOidList(productOid).stream().map(Personnel::getOid).collect(Collectors.toSet()), searchKey);
    }

    @Override
    public ProductAndLicenseInfo findByOid(String oid) {
        Assert.notBlank(oid, "product.oid.not.black");
        Product product = commonService.findByOid(Product.TYPE, oid, Product.class);

        Assert.notNull(product, "product.not.exits");

        ProductLicense productLicense = getProductLicense(product);

        ProductAndLicenseInfo productAndLicenseInfo = new ProductAndLicenseInfo();
        BeanUtil.copyProperties(product, productAndLicenseInfo);

        productAndLicenseInfo.setMemberNumAllow(productLicense.getMemberNumAllow());
        productAndLicenseInfo.setActiveDate(DateUtil.formatDate(new Date(productLicense.getActiveDate())));
        productAndLicenseInfo.setExpirationDate(DateUtil.formatDate(new Date(productLicense.getExpirationDate())));

        return productAndLicenseInfo;
    }

    @Override
    public int verifyAccess(String account, String productKey) {
        return productService.verifyAccess(account, productKey);
    }

    private Product findByName(String name) {
        return commonService.dynamicQueryOne(Product.TYPE,
                Condition.where("name").eq(name),
                POJO_CLASS);
    }

    private Product findByProductKey(String productKey) {
        return commonService.dynamicQueryOne(Product.TYPE,
                Condition.where("productKey").eq(productKey),
                POJO_CLASS);
    }

    private Condition buildProductCondition(String searchKey, String categoryOid) {
        Condition condition;
        if(categoryOid.equals("all")) {
            condition = Condition.where("type").eq("Product");
        } else {
            condition = Condition.where("categoryOid").eq(StringUtils.isBlank(categoryOid) ? StringUtils.EMPTY :
                    categoryOid);
        }
        if (StringUtil.isNotBlank(searchKey)) {
            condition.and(Condition.where("name").contain(searchKey)
                    .or(Condition.where("productKey").contain(searchKey))
                    .or(Condition.where("displayName").contain(searchKey)));
        }
        return condition;
    }

    private Condition buildAccessTargetCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("number").contain(searchKey))
                    .or(Condition.where("associatedAccount").contain(searchKey))
                    .or(Condition.where("displayName").contain(searchKey));
        }
        return condition;
    }

    private Order buildOrder() {
        Order categoryOrder = new Order().desc("name");
        return categoryOrder;
    }

    private String transformResponseInput2String(String fileOid) {
        Response response = iamFileRemote.downloadByFileOid(fileOid);
        Assert.isTrue(response.status() == 200, String.format(Locale.ENGLISH, "File Download fail fileOid:%s",
                fileOid));
        return response.body().toString();
    }
}

