package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.annotation.Description;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class PersonnelCreateDTO {

    @NotBlank
    @ApiModelProperty("编号，工号")
    private String number;

    @NotBlank
    @ApiModelProperty("名称国际化key")
    private String name;

    @NotBlank
    @ApiModelProperty("显示名称")
    private String displayName;

    @NotBlank
    @ApiModelProperty("关联账号")
    private String associatedAccount;

    @NotBlank
    @ApiModelProperty("岗位id")
    private String positionNumber;

    @ApiModelProperty("组织oid 岗位下直接添加人员定位岗位归属")
    private String orgOid;

    @NotBlank
    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("个人邮箱")
    @NotBlank
    private String email;

    @ApiModelProperty("描述说明")
    private String description;

    @Description("扩展属性")
    private JSONObject extensionContent;

}
