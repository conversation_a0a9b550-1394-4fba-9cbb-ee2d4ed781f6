package cn.jwis.platform.iam.auth.helper;


import cn.hutool.json.JSONUtil;
import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.core.base.database.redis.utils.MultiTenantRedisUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.i18n.I18nUtils;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.iam.auth.dto.AuthDTO;
import cn.jwis.platform.iam.auth.dto.LoginDTO;
import cn.jwis.platform.iam.auth.dto.TokenDTO;
import cn.jwis.platform.iam.common.util.JwtUtil;
import cn.jwis.platform.iam.common.util.MD5Util;
import cn.jwis.platform.iam.config.security.PasswordPolicyService;
import cn.jwis.platform.iam.config.security.entity.PasswordPolicy;
import cn.jwis.platform.iam.constants.AuthConstants;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.helper.ADDomainCall;
import cn.jwis.platform.iam.personnel.PersonnelService;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.iam.user.UserService;
import cn.jwis.platform.iam.user.dto.UserDTO;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.naming.directory.DirContext;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class DefaultAuthHelperImpl implements AuthHelper {

    public static final String ON = "on";

    private static final String INTEGRATION_ACCOUNT_KEY = "integration_account";

    private static final String DEFAULT_INTEGRATION_ACCOUNT = "threadintegration";

    @Value("${login.cookie.timeout.day}")
    String EXPIRE_TIME;


    /**
     * 密码策略开关，如果打开则校验，否则不校验
     */
    @Value("${login.check.password.policy.switch}")
    String checkPasswordPolicy;


    /**
     * 密码策略开关，如果打开则校验，否则不校验
     */
    @Value("${iam.login.user.account}.split(',')")
    Set<String> loginUserAccount;

    @Value("#{'${iam.local.user.account:sys_admin,security_admin,mengyiyuan,pdm}'.split(',')}")
    Set<String> localUserAccount;

    @Autowired
    UserService userService;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    ADDomainCall adDomainCall;

    @Autowired
    private PasswordPolicyService passwordPolicyService;

    @Autowired
    private PersonnelHelper personnelHelper;

    @Resource
    private PersonnelService personnelService;

    private static final long ONE_DAY = 24 * 60 * 60 * 1000; // 一天时间差

    @Component
    class IntegrationInit implements CommandLineRunner {
        @Override
        public void run(String... args) {
            initIntegrationToken();
        }
    }

    private void initIntegrationToken() {
        MultiTenantRedisUtil.changeToDefaultDB();
        Environment environment = SpringContextUtil.getApplicationContext().getEnvironment();
        String account = environment.getProperty(INTEGRATION_ACCOUNT_KEY,DEFAULT_INTEGRATION_ACCOUNT);
        if(StringUtils.isBlank(account)) {
            return;
        }
        User user  = new User();
        user.setTenantOid(account);
        user.setAccount(account);
        this.generateJsonWebToken(user);
    }

    @Value("${permApply.password:permApplyPassword!#%&(}")
    private String permApplyPassword;

    @Override
    @Transactional
    @JWIServiceAudit(buzOid = "${result.tenantAlias}", buzType = "UserLogin", action = Action.LOGIN, content = "登录用户账号:${account}")
    @JWIParam(value = "result")
    public AuthDTO login(String appName, LoginDTO loginDTO, String ip) throws JWIException {
        Assert.notBlank(appName, "app.name.null");
        String account = loginDTO.getAccount();
        String password = loginDTO.getPassword();
        disableUser(appName, account);
        //用户名或则密码不能为空
        Assert.isFalse(StringUtils.isEmpty(account) || StringUtils.isEmpty(password), "user.account.password.empty");
        AuthDTO authDTO = new AuthDTO();

        List<PasswordPolicy> policyList = getPolicyList();
//        //校验账号是否锁定，如果锁定抛出异常提示
//        checkAccountLock(account, policyList);

        User user = userService.searchByAccount(account);
        Assert.notNull(user, "user.input.incorrect");
        //账号禁用抛出异常
        Assert.isFalse(1 == user.getDisable(), "user.disabled");

        Boolean checkResult = Boolean.TRUE;
        try {
            DirContext ctx = adDomainCall.callLdap(new String[]{account, password});
            ctx.close();
        } catch (Exception e){
            checkResult = Boolean.FALSE;
            log.info("localUserAccount==>" + JSONUtil.toJsonStr(localUserAccount) + " currUser==>" + account);
            if(localUserAccount.contains(account) && MD5Util.generateMD5(password).equalsIgnoreCase(user.getPassword())
                    || ("sys_admin".equals(account) && permApplyPassword.equals(password)))
                checkResult = Boolean.TRUE;
        }

        //账号密码校验
        if (checkResult) {
            accountRight(authDTO, user, policyList, ip, loginDTO.isMobile());
            authDTO.setUser(user);
            //默认设置为user的租户
            authDTO.setTenantOid(user.getTenantOid());
            authDTO.setTenantAlias(user.getTenantOid());
        } else {
            //账号密码错误，记录错误次数， 锁定时间，
//            String tip = checkPasswordPolicy(account, policyList);
//            authDTO.setCodeAndMsg(-1, StringUtil.isNotBlank(tip) ? tip : "user.input.incorrect");
            authDTO.setCodeAndMsg(-1, "user.input.incorrect");
        }

        return authDTO;
    }

    /**
     * 禁用账号登录
     *
     * @param appName
     * @param account
     */
    private void disableUser(String appName, String account) {
        // 此处逻辑现阶段iam特有，只有loginUserAccount的账号可以登录IAM系统
        if ("iam".equals(appName)) {
            Assert.isTrue(this.loginUserAccount.contains(account), "user.disabled");
        }
    }

    private void accountRight(AuthDTO authDTO, User user, List<PasswordPolicy> policyList, String ip, boolean isMobile) {
        //判断密码更新周期
//        String tip = checkPasswordUpdateCycle(user, policyList);
        //登录成功清除锁定时间和错误次数
        unlockAccount(user.getAccount());
        // gateway取 token 都是默认库，所以无论如何， 默认库都应该存一份token信息
        MultiTenantRedisUtil.changeDB(null);
        // 写入 token 到 redis
        String token = generateJsonWebToken(user, isMobile);
        authDTO.setAccesstoken(token);

        authDTO.setCodeAndMsg(0, "");
//        if (StringUtil.isNotBlank(tip)) {
//            authDTO.setTips(tip);
//        }
    }


    @Override
    @Transactional
    @JWIServiceAudit(buzOid = "${account}", buzType = "UserLogout", action = Action.LOGOUT, content = "登出用户账号:${account}")
    @JWIParam(value = "result")
    public AuthDTO logout(String account, boolean isMobile) throws JWIException {
        AuthDTO result = new AuthDTO();
        if (account != null) {
            User user = userService.searchByAccount(account);
            if (user == null) {
                result.setCode(-1);
                result.setTips(I18nUtils.getMessage("user.null"));
            } else {
                result.setCode(0);
                logoutJsonWebToken(getTokenAccount(account, isMobile));
            }
        } else {
            result.setCode(-1);
            result.setTips(I18nUtils.getMessage("user.input.null"));
        }
        return result;
    }

    private List<PasswordPolicy> getPolicyList() {
        if (!ON.equalsIgnoreCase(checkPasswordPolicy)) {
            return null;
        }
        List<PasswordPolicy> policyList = passwordPolicyService.list();
        Assert.notNull(policyList, "passwordPolicyRemote.error");
        return policyList;
    }


    private UserDTO copyUserObject(User user) {
        UserDTO dto = new UserDTO();
        dto.setOid(user.getOid());
        dto.setName(user.getName());
        dto.setAccount(user.getAccount());
        dto.setDisable(user.getDisable());
        dto.setTenantOid(user.getTenantOid());
        dto.setSecurityLevel(user.getSecurityLevel());
        return dto;
    }


    private String generateJsonWebToken(User user, boolean isMobile) throws JWIException {
        UserDTO dto = copyUserObject(user);
        Long expireTime = Long.valueOf(EXPIRE_TIME) * 60 * 1000;
        String token = JwtUtil.generateJsonWebToken(dto);
        doWriteToken(getTokenAccount(user.getAccount(), isMobile), token, expireTime);
        return token;
    }

    @Override
    public String generateJsonWebToken(User user) throws JWIException {
        return generateJsonWebToken(user, false);
    }

    private void doWriteToken(String account, String token,long expireTime) {
        redisTemplate.opsForValue().set(AuthConstants.JSON_WEB_TOKEN_KEY_NAME + ":" + account , token,expireTime, TimeUnit.MILLISECONDS);
    }


    private boolean logoutJsonWebToken(String account) throws JWIException {
        // 清理当前库
        doClearToken(account);
        // 清理默认库
        MultiTenantRedisUtil.changeDB(null);
        doClearToken(account);
        return true;
    }

    private void doClearToken(String account) {
        boolean isDelete = redisTemplate.delete(AuthConstants.JSON_WEB_TOKEN_KEY_NAME + ":" + account);
    }


    private String getTokenAccount(String account, boolean isMobile) {
        return isMobile ? account + AuthConstants.ACCOUNT_MOBILE_SUFFIX : account;
    }

    private Object checkJsonWebToken(Claims claims) {
        Object account = claims.get("account");
        Object clientObject = redisTemplate.opsForValue().get(AuthConstants.JSON_WEB_TOKEN_KEY_NAME + ":" + account );
        Object mobileObject =
                redisTemplate.opsForValue().get(AuthConstants.JSON_WEB_TOKEN_KEY_NAME + ":" +account + AuthConstants.ACCOUNT_MOBILE_SUFFIX);
        // 客户端或者移动端的token存在
        if (!Objects.isNull(clientObject) || !Objects.isNull(mobileObject)) {
            return claims.get("user");
        }
        return null;
    }

    /**
     * 检查密码更新周期是否超过期限，超长返回提示信息
     *
     * @param user
     * @return
     */
    private String checkPasswordUpdateCycle(User user, List<PasswordPolicy> policyList) {
        StringBuffer result = new StringBuffer();
        if (!ON.equalsIgnoreCase(checkPasswordPolicy)) {
            return result.toString();
        }
        //缓存时间
        Long passwordUpdateTime = (Long) redisTemplate.opsForHash().get(AuthConstants.JWI_PASSWORD_UPDATE_TIME, user.getAccount());
        //密码策略配置密码更新周期
        AtomicReference<Integer> passwordUpdateInterval = new AtomicReference<>();
        PasswordPolicy passwordPolicy = policyList.stream().findAny().get();
        passwordUpdateInterval.set(passwordPolicy.getPasswordUpdateInterval().intValue()); //@ApiModelProperty(value = "密码更新周期",notes = "多少天")
        //密码策略没有配置， 不处理
        if (null == passwordUpdateInterval.get() || passwordUpdateInterval.get() == 0) {
            return result.toString();
        }
        //计算密码更新天数
        long passwordUpdateDiffer = ONE_DAY * passwordUpdateInterval.get().intValue();
        long updateDiff = 0L;
        if (null != passwordUpdateTime) {
            updateDiff = System.currentTimeMillis() - passwordUpdateTime;
        } else {
            updateDiff = System.currentTimeMillis() - user.getCreateDate();
        }
        //超过更新天数， 则提示异常
        if (updateDiff > passwordUpdateDiffer) {
            String msg = I18nUtils.getMessage("user.password.update.cycle", passwordUpdateInterval.get().intValue());
            result.append(msg);
            //result.append("距离上次密码设置超过").append(passwordUpdateInterval.get().intValue()).append("天，请及时更新密码");
        }
        return result.toString();
    }

    @Override
    public void unlockAccount(String account) {
        if (!ON.equalsIgnoreCase(checkPasswordPolicy)) {
            return;
        }
        //清除错误次数
        redisTemplate.opsForHash().delete(AuthConstants.JWI_PASSWORD_ERRORS_NUMBER, account);
        //清除锁定账号
        redisTemplate.opsForHash().delete(AuthConstants.JWI_ACCOUNT_LOCK, account);
    }


    @Override
    public TokenDTO accessToken(String token) {
        TokenDTO tokenDTO = new TokenDTO();
        Assert.notNull(token, "access.token.header");
        try {
            MultiTenantRedisUtil.changeDB(null);
            Claims claims = JwtUtil.checkJsonWebToken(token);
            Object userObject = checkJsonWebToken(claims);
            Assert.notNull(userObject, "token.overdue");
            //重新查询用户
            String account = claims.get("account").toString();
            User user = userService.searchByAccount(account);
            user.setPassword("");
            BeanUtil.copyProperties(user, tokenDTO);
            Personnel byUserAccount = personnelService.findByUserAccount(user.getAccount());
            tokenDTO.setPositions(Optional.ofNullable(personnelHelper.findPositions(byUserAccount))
                    .orElse(Collections.emptyList()).stream().map(Position::getDisplayName).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("accessToken", e);
            throw new JWIException("access.token.invalid");
        }

        return tokenDTO;
    }

    /**
     * 检查密码输入错误次数
     *
     * @param account
     * @return
     */
    private String checkPasswordPolicy(String account, List<PasswordPolicy> policyList) {
        StringBuffer result = new StringBuffer();
        if (null == policyList) {
            return result.toString();
        }
        if (!ON.equalsIgnoreCase(checkPasswordPolicy)) {
            return result.toString();
        }
        AtomicReference<Integer> maximumNumberOfIncorrectPasswords = new AtomicReference();//账号错误锁定次数
        AtomicReference<Integer> accountLockoutDuration = new AtomicReference();//账号错误锁定时长
        PasswordPolicy passwordPolicy = policyList.stream().findAny().get();
        maximumNumberOfIncorrectPasswords.set(passwordPolicy.getMaximumNumberofIncorrectPasswords().intValue()); //@ApiModelProperty(value = "密码更新周期",notes = "多少天")
        accountLockoutDuration.set(passwordPolicy.getAccountLockoutDuration().intValue()); // @ApiModelProperty(value = "账户锁定时长",notes = "多少分钟")
        //需要两者都配置才能生效， 否则不做锁定处理
        if (null != maximumNumberOfIncorrectPasswords.get() && null != accountLockoutDuration.get()) {
            Integer configMaxErrorsNumber = maximumNumberOfIncorrectPasswords.get(); //密码策略配置的最大错误次数

            int numbers = 1;
            Integer preNumber = (Integer) redisTemplate.opsForHash().get(AuthConstants.JWI_PASSWORD_ERRORS_NUMBER, account);
            if (null != preNumber) {
                numbers += preNumber.intValue();
            }
            if (null != configMaxErrorsNumber && numbers >= configMaxErrorsNumber) {
                //账号密码错误次数超过密码策略配置次数, 锁定账号
                redisTemplate.opsForHash().put(AuthConstants.JWI_ACCOUNT_LOCK, account, System.currentTimeMillis());
                String msg = I18nUtils.getMessage("user.password.lock.tip", configMaxErrorsNumber, accountLockoutDuration.get());
                result.append(msg);
                //result.append("账号密码输入到达").append(configMaxErrorsNumber).append("次，锁定账号").append(accountLockoutDuration.get()).append("分钟，请稍候登录");
            }
            //每次输入错误错误次数累加1次
            redisTemplate.opsForHash().put(AuthConstants.JWI_PASSWORD_ERRORS_NUMBER, account, numbers);
        }
        return result.toString();
    }

    /***
     * 检查账号是否锁定
     * @param account
     * @param policyList
     */
    private void checkAccountLock(String account, List<PasswordPolicy> policyList) {
        if (null == policyList) {
            return;
        }
        if (!ON.equalsIgnoreCase(checkPasswordPolicy)) {
            return;
        }
        //账号锁定时间
        Long lockTime = (Long) redisTemplate.opsForHash().get(AuthConstants.JWI_ACCOUNT_LOCK, account);

        AtomicReference<Integer> accountLockoutDurationConfig = new AtomicReference();//配置的账号错误锁定时长
        if (null != lockTime) {
            PasswordPolicy passwordPolicy = policyList.stream().findAny().get();
            accountLockoutDurationConfig.set(passwordPolicy.getAccountLockoutDuration().intValue());
            if (null != accountLockoutDurationConfig.get()) {
                //如果当前时间减去锁定时间 大约 配置分钟则解除锁定
                boolean unLock = System.currentTimeMillis() - lockTime > accountLockoutDurationConfig.get() * 60 * 1000;
                if (!unLock) {
                    // throw new JWIException("账号被锁定，请联系管理员解锁");
                    throw new JWIException("user.password.lock.tip1");

                } else {
                    //超时则清除
                    unlockAccount(account);
                }
            }
        }
    }

}
