package cn.jwis.platform.iam.event;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2022/11/25
 * @Description :
 */
public interface DataDistributionService {

    /**
     * 通过MQ发送数据
     *
     * @param appKey 应用key，如pdm
     * @param opType 操作类型 如create,update,delete
     * @param subject 主题 如Company，User
     * @param jsonObj 发送的数据
     * @return
     */
    boolean sendDataToMq(String appKey,String opType, String subject,JSONObject jsonObj);
}
