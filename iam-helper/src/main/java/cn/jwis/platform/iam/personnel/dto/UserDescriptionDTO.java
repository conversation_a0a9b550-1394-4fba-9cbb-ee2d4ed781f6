package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/22
 * @Description : 备注请求
 */
@Data
public class UserDescriptionDTO {
    @NotBlank
    @Description("用户oid")
    private String userOid;

    @Size(max = 32)
    @Description("用户备注")
    private String comments;

    @Size(max = 255)
    @Description("用户描述")
    private String description;
}
