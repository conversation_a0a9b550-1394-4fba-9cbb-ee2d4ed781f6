package cn.jwis.platform.iam.product.scheduler;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.iam.organization.helper.CompanyHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/14
 * @Description :授权超期计算定时任务
 */
@Component
public class ADDomainRefreshScheduler {

    private static final Logger logger = LoggerFactory.getLogger(ADDomainRefreshScheduler.class);

    private static final String SCHEDULER_KEY = "AD_DOMAIN_REFRESH_SCHEDULER_KEY";

    @Resource
    CompanyHelper companyHelper;

    @Resource
    RedisTemplate redisTemplate;
    /**
     * 每天定时2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    private void refresh() {
        boolean result = redisTemplate.opsForValue().setIfAbsent(SCHEDULER_KEY, System.currentTimeMillis(), 3, TimeUnit.SECONDS);
        if (!result) {
            logger.info("AD_DOMAIN_REFRESH_SCHEDULER is running cancel");
            return;
        }
        logger.info("start refresh adDomain Data!");
        try {
            UserDTO user = new UserDTO();
            user.setAccount("sys_admin");
            user.setOid("sys_admin");
            user.setTenantOid("");
            SessionHelper.addCurrentUser(user);
            companyHelper.refreshADDomainData();
        }catch (Exception e){
            logger.error("ADDomainRefreshScheduler.refresh.error",e);
        }
        logger.info("end refresh adDomain Data!");
        redisTemplate.delete(SCHEDULER_KEY);
    }
}
