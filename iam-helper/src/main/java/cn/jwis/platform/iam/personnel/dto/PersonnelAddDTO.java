package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.annotation.Description;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> yefei
 * @Description:指定岗位添加用参数dto
 */
@Data
@ApiModel
public class PersonnelAddDTO {

    @NotEmpty
    @Description("人员oid")
    private String personnelOid;

    @NotBlank
    @Description("岗位实例oid")
    private String positionNumber;
}
