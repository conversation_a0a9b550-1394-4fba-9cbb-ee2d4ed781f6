package cn.jwis.platform.iam.common.util;

import cn.jwis.platform.iam.user.dto.UserDTO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;

@Component
public class JwtUtil {


    //token 密钥
    private static final String TOKEN_SECRET = "ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=";

    //通过用户对象  新建一个用户的token
    public static String generateJsonWebToken(UserDTO dto) {
        Key key;
        byte[] keyBytes = Decoders.BASE64.decode(TOKEN_SECRET);
        key = Keys.hmacShaKeyFor(keyBytes);
        return Jwts.builder()
                .setIssuer("JWIS")   //签发者
                .setIssuedAt(new Date())   //签发时间
                .setSubject(dto.getAccount())  //主题
//                .setExpiration(new Date(System.currentTimeMillis() + EXPIRE_TIME))  //到期时间
                .claim("user", dto)   //添加 自定义claims  注意不要和内置的属性重复
                .claim("account", dto.getAccount())
                .claim("userOid", dto.getOid())
                .signWith(key, SignatureAlgorithm.HS256)  //签发密钥以及签发算法
                .compact();
    }


    //解析JWT字符串
    public static Claims checkJsonWebToken(String token) {
        try {
            final Claims claims = Jwts.parser().setSigningKey(TOKEN_SECRET).parseClaimsJws(token).getBody();
            return claims;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
