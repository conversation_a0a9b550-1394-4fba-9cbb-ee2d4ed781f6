package cn.jwis.platform.iam.auth.dto;

import cn.jwis.platform.iam.device.Device;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2022/11/24
 * @Description :
 */

@Data
public class LoginDTO {

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("手机端信息")
    private Device device;

    /**
     * 是否移动端
     */
    @ApiModelProperty("是否移动端")
    private boolean mobile;


}
