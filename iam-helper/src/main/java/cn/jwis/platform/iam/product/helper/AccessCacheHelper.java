package cn.jwis.platform.iam.product.helper;

import cn.jwis.core.base.database.redis.utils.MultiTenantRedisUtil;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.constants.ActionConstants;
import cn.jwis.platform.iam.constants.RedisConstants;
import cn.jwis.platform.iam.event.NoticeHelper;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.helper.CompanyHelper;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.user.User;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/25
 * @Description :授权信息缓存
 */
@Component
public class AccessCacheHelper {

    @Resource
    ProductHelper productHelper;

    @Resource
    PersonnelHelper personnelHelper;

    @Resource
    CompanyHelper companyHelper;

    @Resource
    NoticeHelper noticeHelper;

    @Resource
    RedisTemplate redisTemplate;

    @Resource
    CommonService commonService;

    @Component
    class CacheLoader implements CommandLineRunner {
        @Override
        public void run(String... args) {
            loadCache();
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(AccessCacheHelper.class);

    public Set<String> getPerByProductKey(String productKey) {
        Map<String,Set<String>> preMap = redisTemplate.opsForHash().entries(RedisConstants.MAP_PER_PRODUCT_KEY_NAME);
        return preMap.get(productKey);
    }

    public Set<String> getOrgByProductKey(String productKey) {
        Map<String,Set<String>> orgMap = redisTemplate.opsForHash().entries(RedisConstants.MAP_ORG_PRODUCT_KEY_NAME);
        return orgMap.get(productKey);
    }

    public Map<String, Set<String>> findCache(Set<String> oidSet, String type) {
        Map<String, Set<String>> findResult = new HashMap<>();
        getCacheWithType(type).entrySet().forEach(item -> {
            Set<String> tmpSet = new HashSet<>(oidSet);
            tmpSet.retainAll(item.getValue());
            findResult.put(item.getKey(), tmpSet);
        });
        return findResult;
    }

    private Map<String, Set<String>> getCacheWithType(String type) {
        String key = Personnel.TYPE.equals(type) ? RedisConstants.MAP_PER_PRODUCT_KEY_NAME :
                RedisConstants.MAP_ORG_PRODUCT_KEY_NAME;
        return redisTemplate.opsForHash().entries(key);
    }

    public void handPersonnelChange(String productKey, Set<String> oidSet, String action) {
        if(ActionConstants.ACTION_DELETE.equals(action)) {
            removePersonnelAndRefreshCache(oidSet, productKey);
        } else if(ActionConstants.ACTION_CREATE.equals(action)){
            addPersonnelAndRefreshCache(oidSet, productKey, action);
        } else {
            noticeHelper.sendMessage(findPersonnelList(oidSet), Lists.newArrayList(productKey), User.TYPE, action);
        }
    }

    private void loadCache() {
        // 启动时进行缓存加载，需要手动切换到指定redis库 不指定默认为0
        MultiTenantRedisUtil.changeToDefaultDB();
        cleanPerAndOrgRedisCache();
        // 人员数据 productKey - oid  直接缓存
        Map<String, Set<String>> productKeyOidMap = productHelper.queryAllAccessPersonnelGroupByProductKey();
        logger.info("all personnel access size {}", productKeyOidMap.size());
        redisTemplate.opsForHash().putAll(RedisConstants.MAP_PER_PRODUCT_KEY_NAME,productKeyOidMap);
        productKeyOidMap.keySet().forEach(item->{
            refreshProductKeyAccountCache(item,productKeyOidMap.get(item));
        });
        refreshOrgCacheByPersonnel(productKeyOidMap.keySet());
    }

    private void cleanPerAndOrgRedisCache() {
        redisTemplate.delete(RedisConstants.MAP_PER_KEY_NAME);
        redisTemplate.delete(RedisConstants.MAP_ORG_KEY_NAME);
        redisTemplate.delete(RedisConstants.MAP_ORG_PRODUCT_KEY_NAME);
        redisTemplate.delete(RedisConstants.MAP_PER_PRODUCT_KEY_NAME);
        redisTemplate.delete(RedisConstants.MAP_PER_PRODUCT_KEY_ACCOUNT_NAME);
    }

    private void cleanProductKeyCache() {
        redisTemplate.delete(RedisConstants.MAP_ORG_PRODUCT_KEY_NAME);
        redisTemplate.delete(RedisConstants.MAP_PER_PRODUCT_KEY_NAME);
    }



    public void removePersonnelAndRefreshCache(Set<String> personnelOidSet, String productKey) {
        logger.info("RemovePersonnelAndRefreshCache begin personnel {} , productKey {} ", personnelOidSet, productKey);
        if(CollectionUtils.isEmpty(personnelOidSet)) {
            logger.info("Have nothing to change {} ", productKey);
            return;
        }

        Map<String, Set<String>> accessPerCache =
                redisTemplate.opsForHash().entries(RedisConstants.MAP_PER_PRODUCT_KEY_NAME);

        if (accessPerCache.isEmpty() || ObjectUtils.isEmpty(accessPerCache.get(productKey))) {
            logger.info("can not find product cache {} ", productKey);
            return;
        }
        personnelOidSet.forEach(oid -> {
            accessPerCache.get(productKey).remove(oid);
        });
        redisTemplate.opsForHash().putAll(RedisConstants.MAP_PER_PRODUCT_KEY_NAME, accessPerCache);
        refreshProductKeyAccountCache(productKey, accessPerCache.get(productKey));
        logger.info("RemovePersonnelAndRefreshCache now personnel {} ，productKey {} ", accessPerCache.get(productKey), productKey);
        List<TreeAbleEx> changeList = refreshCacheAndFindDiff(true, productKey);
        logger.info("RemovePersonnelAndRefreshCache after refresh changeList {} , productKey {} ",
                StringUtils.join(changeList.stream().map(TreeAbleEx::getDisplayName).collect(Collectors.toList())),
                productKey);
        changeList.addAll(findPersonnelList(personnelOidSet));
        Collections.reverse(changeList);
        noticeHelper.sendMessage(changeList, ActionConstants.ACTION_DELETE, productKey);
    }

    public void addPersonnelAndRefreshCache(Set<String> personnelOidSet, String productKey,String action) {
        logger.info("AddPersonnelAndRefreshCache begin personnel {} , productKey {} , action {}", personnelOidSet,
                productKey, action);
        Map<String, Set<String>> accessPerCache =
                redisTemplate.opsForHash().entries(RedisConstants.MAP_PER_PRODUCT_KEY_NAME);
        // 人员无变化则跳过操作
        accessPerCache.computeIfAbsent(productKey, key -> Sets.newHashSet()).addAll(personnelOidSet);
        redisTemplate.opsForHash().putAll(RedisConstants.MAP_PER_PRODUCT_KEY_NAME, accessPerCache);

        refreshProductKeyAccountCache(productKey, accessPerCache.get(productKey));
        logger.info("addPersonnelAndRefreshCache now personnel {} ，productKey {} ", accessPerCache.get(productKey), productKey);
        List<TreeAbleEx> changeList = refreshCacheAndFindDiff(false, productKey);
        logger.info("AddPersonnelAndRefreshCache after refresh changeList {} , productKey {} ",
                StringUtils.join(changeList.stream().map(TreeAbleEx::getDisplayName).collect(Collectors.toList())),
                productKey);
        // create 为结构变化提醒，无结构变化则忽略更新
        if(CollectionUtils.isEmpty(changeList) && ActionConstants.ACTION_CREATE.equals(action)) {
            logger.info("AddPersonnelAndRefreshCache org no change cancel");
            return;
        }
        changeList.addAll(findPersonnelList(personnelOidSet));
        noticeHelper.sendMessage(changeList, action, productKey);
    }

    private Set<String> getOidSetFromOrgCache(String key) {
        Map<String, Set<String>> accessOrgCache =
                redisTemplate.opsForHash().entries(RedisConstants.MAP_ORG_PRODUCT_KEY_NAME);
        return accessOrgCache.containsKey(key) ? accessOrgCache.get(key) : Sets.newHashSet();
    }

    private List<TreeAbleEx> refreshCacheAndFindDiff(boolean isDelete, String productKey) {
        Set<String> befSet = new HashSet<>(getOidSetFromOrgCache(productKey));
        refreshOrgCacheByPersonnel(Sets.newHashSet(productKey));
        Set<String> aftSet = new HashSet<>(getOidSetFromOrgCache(productKey));
        Set<String> result;
        if (isDelete) {
            befSet.removeAll(aftSet);
            result = befSet;
        } else {
            aftSet.removeAll(befSet);
            result = aftSet;
        }
        List<TreeAbleEx> changeList = findNoticeOrgOidByOrder(result);
        return changeList;
    }

    private List<? extends TreeAbleEx> findPersonnelList(Set<String> perSet) {
        return personnelHelper.queryUserWithPositionByPersonnelOidList(perSet.stream().collect(Collectors.toList()));
    }

    private List<TreeAbleEx> findNoticeOrgOidByOrder(Set<String> orgOidSet) {
        Map<String,TreeAbleEx> orgMap = companyHelper.getCurrentIdOrgMap();
        List<TreeAbleEx> treeAbleExList =
                orgMap.entrySet().stream().filter(item -> orgOidSet.contains(item.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());

        Collections.sort(treeAbleExList, (o1, o2) -> {
            if (!o1.getType().equalsIgnoreCase(o2.getType())) {
                if (Company.TYPE.equals(o1.getType())) {
                    return -1;
                } else if (Department.TYPE.equals(o1.getType()) && (Position.TYPE.equals(o2.getType()))) {
                    return -1;
                } else {
                    return 1;
                }
            } else {
                if (Company.TYPE.equals(o1.getType())) {
                    Company c1 = (Company) o1;
                    Company c2 = (Company) o2;
                    return c1.getLevel() - c2.getLevel();
                } if (Department.TYPE.equals(o1.getType())) {
                    Department c1 = (Department) o1;
                    Department c2 = (Department) o2;
                    return c1.getLevel() - c2.getLevel();
                } else {
                    return 0;
                }
            }
        });
        return treeAbleExList;
    }

    private void refreshOrgCacheByPersonnel(Set<String> keyList) {
        Map<String, Set<String>> preCache = redisTemplate.opsForHash().entries(RedisConstants.MAP_PER_PRODUCT_KEY_NAME);
        if(ObjectUtils.isEmpty(preCache) || preCache.isEmpty()) {
            cleanProductKeyCache();
            return;
        }

        Map<String, Set<String>> personnelOidProductKeyMap = new HashMap<>();
        // 数据反转为 oid -- productKey结构
        keyList.forEach(productKey->{
            if(preCache.containsKey(productKey)) {
                preCache.get(productKey).forEach(personnelOid -> {
                    personnelOidProductKeyMap.computeIfAbsent(personnelOid, key -> Sets.newHashSet()).add(productKey);
                });
            }
        });

        Map<String, List<TreeAbleEx>> currentOidPerMap = companyHelper.getCurrentIdPerMap();
        Map<String, TreeAbleEx> currentOidOrgMap = companyHelper.getCurrentIdOrgMap();

        Set<String> parentOidSet = Sets.newHashSet();
        Map<String, Set<String>> orgOidProductKeyMap = new HashMap<>();
        // 将所属岗位增加进临时缓存，从总查找清单内移除  将岗位所属部门进行添加
        for(Map.Entry<String,Set<String>> item : personnelOidProductKeyMap.entrySet()) {
            if (!currentOidPerMap.containsKey(item.getKey()) || ObjectUtils.isEmpty(currentOidPerMap.get(item.getKey()))) {
                logger.info("refreshOrgCacheByPersonnel can not find personnel find oidMap Oid{}", item.getKey());
                continue;
            }
            for(TreeAbleEx personnel : currentOidPerMap.get(item.getKey())) {
                TreeAbleEx position = currentOidOrgMap.get(personnel.getParentNodeId());
                if (ObjectUtils.isEmpty(position)) {
                    logger.info("refreshOrgCacheByPersonnel can not find belong position by personnel.getParentNodeId()");
                    continue;
                }
                orgOidProductKeyMap.computeIfAbsent(position.getCurrentNodeId(), (key) -> Sets.newHashSet()).addAll(item.getValue());
                parentOidSet.add(position.getParentNodeId());
                orgOidProductKeyMap.computeIfAbsent(position.getParentNodeId(), (key) -> Sets.newHashSet()).addAll(item.getValue());
                currentOidOrgMap.remove(position.getCurrentNodeId());
            }
        }
        // 广度查找组织结构
        findNextParentLink(parentOidSet, orgOidProductKeyMap, currentOidOrgMap);
        // 将组织结构按照 productKey oid结构进行转换后缓存
        Map<String, Set<String>> orgProductKeyOidMap = new HashMap<>();
        orgOidProductKeyMap.entrySet().forEach(item -> {
            item.getValue().forEach(orgOid -> {
                orgProductKeyOidMap.computeIfAbsent(orgOid, key -> Sets.newHashSet()).add(item.getKey());
            });
        });

        keyList.forEach(productKey->{
            if(ObjectUtils.isEmpty(preCache.get(productKey))) {
                orgProductKeyOidMap.put(productKey,Sets.newHashSet());
            }
        });

        redisTemplate.opsForHash().putAll(RedisConstants.MAP_ORG_PRODUCT_KEY_NAME, orgProductKeyOidMap);
    }

    private void findNextParentLink(Set<String> findCurrentOidSet, Map<String, Set<String>> orgOidProductKeyMap,
                                    Map<String, TreeAbleEx> currentOidOrgMap) {
        if (CollectionUtils.isEmpty(findCurrentOidSet)) {
            return;
        }
        Set<String> nextLevelOidSet = new HashSet<>();
        for (String currentOid : findCurrentOidSet) {
            TreeAbleEx currentNode = currentOidOrgMap.get(currentOid);
            if (ObjectUtils.isEmpty(currentNode)) {
                logger.info("can not find node by {}", currentOid);
                continue;
            }
            if (StringUtils.isBlank(currentNode.getParentNodeId())) {
                continue;
            }
            nextLevelOidSet.add(currentNode.getParentNodeId());
            // 找到父节后将自己测所属应用赋予
            orgOidProductKeyMap.computeIfAbsent(currentNode.getParentNodeId(), key -> Sets.newHashSet()).addAll(orgOidProductKeyMap.get(currentNode.getCurrentNodeId()));
        }
        findNextParentLink(nextLevelOidSet, orgOidProductKeyMap, currentOidOrgMap);
    }

    private void refreshProductKeyAccountCache(String productKey, Set<String> oidSet) {
        if (oidSet == null) {
            oidSet = Sets.newHashSet();
        }
        Set<String> accountSet  = commonService.findByOid(Personnel.TYPE,oidSet.stream().collect(Collectors.toList())
                , Personnel.class).stream().map(Personnel::getAssociatedAccount).collect(Collectors.toSet());
        redisTemplate.opsForHash().put(RedisConstants.MAP_PER_PRODUCT_KEY_ACCOUNT_NAME, productKey, accountSet);
    }

}
