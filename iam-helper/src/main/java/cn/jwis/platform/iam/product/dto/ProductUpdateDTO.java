package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class ProductUpdateDTO {

    @Description("应用名称")
    private String name;

    @Description("显示名称")
    private String displayName;

    @Description("应用描述")
    private String description;

    @Description("应用版本")
    private String productVersion;

    @Description("应用类型")
    private String productType;

    @Description("应用主页")
    private String homePage;

    @Description("应用图标")
    private String icon;

    @NotBlank(message = "应用Oid不能为空")
    @Description("应用oid")
    private String oid;

    @Description("license文件oid 传入时用于更新授权信息")
    private String licenseOid;

    @Description("公钥oid 为空时用历史公钥")
    private String publicKeyOid;
}
