package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class FuzzyPersonnelPageByOrgDTO extends PageSimpleDTO {

    @NotBlank
    @ApiModelProperty("父级类型")
    private String orgType;

    @NotBlank
    @ApiModelProperty("父级OID")
    private String orgOid;

    @ApiModelProperty
    private Boolean quitFlag;

    @NotBlank
    @ApiModelProperty("查询方式 1有效用户 2失效用户 3所有用户")
    private Integer searchType;

}
