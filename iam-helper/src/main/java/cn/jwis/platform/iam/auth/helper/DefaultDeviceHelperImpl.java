package cn.jwis.platform.iam.auth.helper;

import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.device.Device;
import cn.jwis.platform.iam.device.DeviceService;
import cn.jwis.platform.iam.relation.LinkTo;
import cn.jwis.platform.iam.relation.UserLinkDevice;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.iam.user.UserService;
import com.alibaba.excel.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2022/11/24
 * @Description :
 */

@Service
public class DefaultDeviceHelperImpl implements DeviceHelper {

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private UserService userService;

    @Resource
    CommonService commonService;

    @Override
    public void addDevice(Device device, User user) {
        if (!Objects.isNull(device)) {
            Device currentDevice;
            if (StringUtils.isNotBlank(device.getMacAddress())) {
                currentDevice = deviceService.byMacAddress(device.getMacAddress());
            } else {
                currentDevice = deviceService.byDeviceId(device.getDeviceId());
            }
            if (ObjectUtils.isEmpty(currentDevice)) {
                device.setOid(OidGenerator.newOid());
                currentDevice = deviceService.addDevice(device);
                deviceService.linkUser(user, currentDevice);
            } else {
                Device linkDevice = deviceService.byUserAndDeviceMacOrId(user, device.getMacAddress(),
                        device.getDeviceId());
                // 和用户没有关系，绑定关系
                if (Objects.isNull(linkDevice)) {
                    deviceService.linkUser(user, currentDevice);
                } else {
                    UserLinkDevice userLinkDevice = new UserLinkDevice();
                    userLinkDevice.setFromType(User.TYPE);
                    userLinkDevice.setFromOid(user.getOid());
                    userLinkDevice.setToType(Device.TYPE);
                    userLinkDevice.setToOid(linkDevice.getOid());
                    Optional<UserLinkDevice> optional = commonService.findRelation(userLinkDevice).stream().findAny();
                    if (optional.isPresent()) {
                        commonService.updateRelation(optional.get());
                    }
                }
            }
        }
    }

    @Override
    public List<Device> findDeviceByAccount(String account) {
        User user = userService.searchByAccount(account);
        List<Device> devicesList = deviceService.findByUser(user);
        UserLinkDevice userLinkDevice = new UserLinkDevice();
        userLinkDevice.setFromType(User.TYPE);
        userLinkDevice.setType(UserLinkDevice.TYPE);
        userLinkDevice.setFromOid(user.getOid());
        userLinkDevice.setToType(Device.TYPE);
        Map<String,Long> updateTimeMap =
                commonService.findRelation(userLinkDevice).stream().collect(Collectors.toMap(key->key.getToOid(),
                        t->t.getUpdateDate()));
        devicesList.forEach(item->{
            if(updateTimeMap.containsKey(item.getOid()) && 0 != updateTimeMap.get(item.getOid())) {
                item.setUpdateDate(updateTimeMap.get(item.getOid()));
            }
        });
        return devicesList;
    }


}
