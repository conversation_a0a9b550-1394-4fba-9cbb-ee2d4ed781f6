package cn.jwis.platform.iam.response;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/8
 * @Description :excel 数据格式
 */
@Data
public class OrgExcelRow {

    @ExcelProperty("公司")
    private String companyName;

    @ExcelProperty("部门归属")
    private String department;

    @ExcelProperty("岗位")
    private String position;

    @ExcelProperty("工号")
    private String number;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("显示名称")
    private String displayName;

    @ExcelProperty("账号")
    private String associatedAccount;

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty("电话")
    private String phone;
}
