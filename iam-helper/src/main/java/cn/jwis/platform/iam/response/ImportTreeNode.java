package cn.jwis.platform.iam.response;

import com.alibaba.excel.util.StringUtils;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/9
 * @Description :
 */
@Data
public class ImportTreeNode {
    String name;
    String type;
    String oid;
    int id;
    String parentOid;
    String parentType;
    String positionDfOid;
    /**
     * 导入数据构建树结构的子结构清单，Position需要同类型多节点，key为临时id Company Department key为name
     */
    Map<String, Map<String, ImportTreeNode>> childTypeNameMap = new HashMap<>();

    public ImportTreeNode getChild(String type,String name) {
        if(!childTypeNameMap.containsKey(type)) {
            return null;
        }
        if(!childTypeNameMap.get(type).containsKey(name)) {
            return null;
        }
        return childTypeNameMap.get(type).get(name);
    }

    public ImportTreeNode(String name,String type) {
        this(name,type, StringUtils.EMPTY);
    }

    public ImportTreeNode(String name,String type,String oid) {
        this.name = name;
        this.type = type;
        this.oid = oid;
    }

    public void addChild(String type, String key, ImportTreeNode node) {
        childTypeNameMap.computeIfAbsent(type, uKey -> Maps.newHashMap()).put(key, node);
    }

}
