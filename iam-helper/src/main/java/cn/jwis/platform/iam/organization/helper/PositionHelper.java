package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.iam.organization.dto.PositionCreateDTO;
import cn.jwis.platform.iam.organization.dto.PositionDeleteDTO;
import cn.jwis.platform.iam.organization.dto.PositionSetUpDTO;
import cn.jwis.platform.iam.organization.dto.PositionUpdateDTO;
import cn.jwis.platform.iam.organization.dto.PositionUpdateSetupDTO;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.personnel.response.PositionWithRel;
import cn.jwis.platform.iam.response.SetUpWithVacancy;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
public interface PositionHelper {

    PositionDef create(PositionCreateDTO dto);

    PositionDef update(PositionUpdateDTO dto);

    Long delete(String oid,String type);

    List<PositionDef> fuzzy(String searchKey);

    PageResult<PositionDef> fuzzyPage(PageSimpleDTO dto);

    PositionDef findByOid(String oid);

    PositionDef findByNumber(String number);

    PositionDef findByName(String name);

    default List<Position> createSetUp(PositionSetUpDTO dto) {
        return this.createSetUp(dto, true);
    }

    List<Position> createSetUp(PositionSetUpDTO dto,boolean checkRepeat);

    SetUpWithVacancy updateSetUp(PositionUpdateSetupDTO dto);

    Long deleteSetUp(PositionDeleteDTO oid);

    PageResult<PositionWithRel> fuzzyByFrom(String fromType, String fromOid, String searchKey,int index,int size);

    /**
     * 岗位导出
     *
     * @param response
     * @throws IOException
     */
    void exportExcel(HttpServletResponse response) throws IOException;


    /**
     * 组织管理导入
     *
     * @param file
     * @return 结果
     */
    boolean importExcel(MultipartFile file);
}
