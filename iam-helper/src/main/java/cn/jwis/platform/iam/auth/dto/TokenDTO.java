package cn.jwis.platform.iam.auth.dto;

import cn.jwis.platform.iam.user.dto.UserDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Title: TokenDTO
 * @ProjectName authentification-service
 * @Description: TODO
 * @date 2019/11/2014:24
 */
@Data
public class TokenDTO extends UserDTO {

    /**
     * 当前组织别名
     */
    String tenantAlias;
    /**
     * 当前组织oid
     */
    String tenantOid;


    /**
     * 关联的岗位
     */
    List<String> positions;
}
