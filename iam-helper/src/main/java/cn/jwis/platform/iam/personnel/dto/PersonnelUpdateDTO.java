package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.annotation.Description;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class PersonnelUpdateDTO {

    @NotBlank
    @Description("人员oid")
    private String oid;

    @Description("姓名")
    private String displayName;

    @Description("名称")
    private String name;

    @Description("编号，工号")
    private String number;

    @ApiModelProperty("关联账号")
    private String associatedAccount;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("个人邮箱")
    private String email;

    @ApiModelProperty("描述说明")
    private String description;

    @Description("所属岗位Oid")
    @JsonIgnore
    private String positionOid;

    @Description("扩展属性")
    private JSONObject extensionContent;
}
