package cn.jwis.platform.iam.organization.dto;

import cn.jwis.platform.iam.response.OrgExcelRow;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/24 16:08
 * @Email <EMAIL>
 */
@Data
public class ADDomainRefreshDTO extends OrgExcelRow {

    public ADDomainRefreshDTO(){
        super.setPosition("成员");
    }

    @ApiModelProperty("部门code")
    private List<String> depCode;

    @ApiModelProperty("公司code")
    private String companyCode;

}
