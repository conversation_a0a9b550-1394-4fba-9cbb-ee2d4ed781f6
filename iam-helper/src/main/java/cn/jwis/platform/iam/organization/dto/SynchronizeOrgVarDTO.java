package cn.jwis.platform.iam.organization.dto;

import cn.jwis.platform.iam.personnel.dto.PersonnelUpdateDTO;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.response.ImportTreeNode;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2024/8/5 16:27
 * @Description :
 */
@Data
public class SynchronizeOrgVarDTO {
    @ApiModelProperty("新增的数据节点")
    private List<ImportTreeNode> shouldBeCreate = new ArrayList<>();

    @ApiModelProperty("更新的数据节点")
    private List<ImportTreeNode> shouldBeUpdate = new ArrayList<>();

    @ApiModelProperty("更新的数据节点 只处理公司部门")
    private List<TreeAbleEx> shouldBeUpdateAble = new ArrayList<>();

    @ApiModelProperty("删除的数据节点 只处理公司部门")
    private List<TreeAbleEx> shouldBeDelete = new ArrayList<>();

    @ApiModelProperty("删除的数据节点 只处理人员")
    private List<Personnel> shouldBeDeletePersonnel = new ArrayList<>();

    @ApiModelProperty("更新的数据节点 只处理人员")
    private List<PersonnelUpdateDTO> shouldBeUpdatePersonnel = new ArrayList<>();

    @ApiModelProperty("岗位临时ID和人员关联")
    Map<Integer, Personnel> positionIdPersonnelMap = new HashMap<>();

    @ApiModelProperty("旧数据人员编码和人员关联")
    Map<String, Personnel> dbAllNumberPersonnelMap = new HashMap<>();
}
