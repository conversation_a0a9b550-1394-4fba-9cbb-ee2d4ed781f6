package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.iam.organization.dto.CompanyCreateDTO;
import cn.jwis.platform.iam.organization.dto.CompanyUpdateDTO;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.response.LevelNodeInfo;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.structure.TreeNode;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> yefei
 */
public interface CompanyHelper {

    Company create(CompanyCreateDTO dto);

    Company createRoot(CompanyCreateDTO dto);

    Company update(CompanyUpdateDTO dto);

    Long delete(String oid,String type);

    List<Company> fuzzy(String searchKey);

    PageResult<Company> fuzzyPage(PageSimpleDTO dto);

    /**
     * 查询全量组织结构树
     *
     * @param buildType 未分配人员岗位展示规则 1 不展示 2只展示 3 全展示
     * @return 顶点
     */
    TreeNode findOrgTree(Integer buildType);

    /**
     * 查询全量组织结构树
     *
     * @param buildType 未分配人员岗位展示规则 1 不展示 2 只展示 3 全展示
     * @param searchKey 搜索关键字
     * @return 顶点
     */
    TreeNode fuzzyOrgTree(String searchKey,Integer buildType);

    /**
     * 根据人员授权清单，组装树结构人员
     *
     * @param personnelOidSet 已授权人员
     * @param searchKey 搜索关键字
     * @return 顶点
     */
    TreeNode findOrgTreeWithAccessFlag(Set<String> personnelOidSet, String searchKey);

    Map<String, TreeAbleEx> getCurrentIdOrgMap();

    Map<String, List<TreeAbleEx>> getCurrentIdPerMap();

    Map<String, List<TreeAbleEx>> getParentIdOrgMap();


    Company findByOid(String oid);

    Company findByName(String name);

    /**
     * 查询指定父节点下内容
     * @param oid 为空时为根节点下内容
     * @return
     */
    LevelNodeInfo queryByLevel(String oid);


    /**
     * 根据给出的节点id搜索父级构建完整结构树
     *
     * @param nodeList
     * @return 完成结构树清单
     */
    List<TreeAbleEx> findLinkParentWithNodeList(List<TreeAbleEx> nodeList);

    /**
     * 组织管理导出
     *
     * @param fromType
     * @param fromOid
     * @param response
     * @throws IOException
     */
    void exportExcel(String fromType, String fromOid, HttpServletResponse response) throws IOException;


    /**
     * 组织管理导入
     *
     * @param file
     * @return 结果
     */
    String importExcel(MultipartFile file);

    /**
     * 一键从AD域同步组织人员信息，并更新本地的组织人员树。 需要提前配置好AD域的链接信息
     * @return
     */
    String refreshADDomainData();
}
