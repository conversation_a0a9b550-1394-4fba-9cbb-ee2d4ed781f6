package cn.jwis.platform.iam.account.dto;

import cn.jwis.platform.plm.foundation.numberrule.entity.valueObj.NumberRuleSegment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
@Data
public class AccountRuleCreateDTO {
    @NotBlank(message = "显示名称不能为空")
    @ApiModelProperty(value = "显示名称",required = true)
    private String displayName ;

    @NotBlank(message = "唯一标识不能为空")
    @ApiModelProperty(value = "编码",required = true)
    private String code;

    @ApiModelProperty(value = "编码规则",required = true)
    private List<NumberRuleSegment> numberRuleSegments;

    @ApiModelProperty(value = "上下文type")
    private String containerType;

    @ApiModelProperty(value = "上下文oid")
    private String containerOid;

    @ApiModelProperty(value = "是否自动生成账户")
    @NotNull(message = "是否默认生成账号不能为空")
    private Boolean autoCreate;

    @ApiModelProperty(value = "账号规则类型1工号生成 2 自定义生成")
    @NotNull(message = "账号规则类型不能为空")
    private Integer accountRuleType;

}
