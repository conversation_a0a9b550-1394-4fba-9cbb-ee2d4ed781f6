package cn.jwis.platform.iam.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 *
 */
@Data
@ApiModel
public class PositionAddPersonnelDTO {

    @NotBlank
    @ApiModelProperty("部门Oid")
    private String orgOid;

    @ApiModelProperty("岗位定义oid")
    private String positionOid;

    @ApiModelProperty("人员Oid")
    private String personnelOid;
}
