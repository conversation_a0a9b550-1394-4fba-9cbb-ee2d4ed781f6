package cn.jwis.platform.iam.user.dto;

import cn.jwis.platform.plm.foundation.attachment.entity.File;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;


/**
 * IndexedColors.RED.getIndex()
 *
 * <AUTHOR>
 */
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
@Data
public class UserDTO {

    @ExcelIgnore
    @ApiModelProperty("用户唯一标识")
    private String oid;

    @ExcelIgnore
    @ApiModelProperty("组织/租户Oid")
    private String tenantOid;

    @ExcelIgnore
    @ApiModelProperty("组织/租户名称")
    private String tenantName;

    @ExcelProperty(value = "账号", index = 1)
    @ApiModelProperty("账号")
    private String account;

    @ExcelProperty(value = "用户名称", index = 0)
    @ApiModelProperty("名称")
    private String name;

    @ExcelIgnore
    @ApiModelProperty("密码")
    private String password;

    @ExcelProperty(value = "联系方式", index = 2)
    @ApiModelProperty("电话")
    private String phone;

    @ExcelProperty(value = "邮箱地址", index = 3)
    @ApiModelProperty("邮箱地址")
    private String email;

    @ExcelIgnore
    @ApiModelProperty("性别,0:男，1:女")
    private int gender;

    @ExcelIgnore
    @ApiModelProperty("描述")
    private String description;

    @ExcelIgnore
    @ApiModelProperty("头像")
    private String avatar;

    @ExcelIgnore
    @ApiModelProperty("disable，0：可用，1：禁用")
    private int disable;

    @ExcelIgnore
    @ApiModelProperty("用户密级")
    private int securityLevel;
    //非neo4j数据表字段，代码迁移返回嗝前端的组织图标，页面不展示可以去除
    @ExcelIgnore
    @ApiModelProperty("图标")
    private String icon;

    @ExcelIgnore
    @ApiParam(value = "老密码")
    private String oldPassword;

    @ExcelIgnore
    @ApiParam(value = "确认密码")
    private String confirmPassword;

    @ExcelIgnore
    @ApiParam(value = "新密码")
    private String newPassword;

    @ExcelIgnore
    @ApiParam(value = "密码更新周期超过时长提示信息")
    private String tips;

    @ExcelIgnore
    @ApiParam(value = "失效时间显示字段")
    private String invalidTime;


    @ExcelIgnore
    private String createBy;

    @ExcelIgnore
    @ApiModelProperty("签名文件")
    private File signature;

    @ExcelIgnore
    private long createDate;
    @ExcelIgnore
    private String updateBy;
    @ExcelIgnore
    private long updateDate;

}
