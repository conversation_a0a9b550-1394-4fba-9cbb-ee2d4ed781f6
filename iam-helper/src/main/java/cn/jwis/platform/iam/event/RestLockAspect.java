package cn.jwis.platform.iam.event;

import cn.jwis.framework.base.exception.JWIServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/14
 * @Description :处理并发锁
 */
@Aspect
@Order(Integer.MAX_VALUE/2)
@Component
@Slf4j
public class RestLockAspect {

    private static final String METHOD_SIGNATURE = "methName:{0}returnType:{1}";
    private static final Logger logger = LoggerFactory.getLogger(RestLockAspect.class);

    @Resource
    RedissonClient redissonClient;

    @Value(value = "${open.rest.lock:true}")
    private boolean openLock;

    @Pointcut("@annotation(cn.jwis.platform.iam.event.RestLock)")
    public void NodeChangeAspect() {
    }

    @Before(value = "NodeChangeAspect()")
    private void before(JoinPoint joinPoint) {
        if(!openLock) {
            return;
        }
        long threadId = Thread.currentThread().getId();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methName = signature.getMethod().getName();
        logger.info("=====================>  Before execute 【"+methName+"】  Method " + threadId );
        // 加锁
        RLock codeLock = redissonClient.getLock(getMethodSignature(joinPoint));
        boolean getLock;
        try {
            getLock = codeLock.tryLock(5, 10, TimeUnit.SECONDS);
            logger.info("=====================> Before getLock result " + getLock + " thread id " + threadId);
            Assert.isTrue(!getLock || codeLock.isHeldByCurrentThread(), "System busy!");
        } catch (InterruptedException e) {
            logger.info("=====================> Before" + e.getMessage() + " thread id " + threadId);
            throw new JWIServiceException(e);
        }

    }

    @AfterReturning(value = "NodeChangeAspect()")
    private void after(JoinPoint joinPoint) {
        if(!openLock) {
            return;
        }
        long threadId = Thread.currentThread().getId();
        RLock codeLock = redissonClient.getLock(getMethodSignature(joinPoint));
        if (codeLock.isLocked()&&codeLock.isHeldByCurrentThread()) {
            logger.info("=====================>  After unlock " + true + " thread id " + threadId);
            codeLock.unlock();
        } else{
            logger.info("=====================>  After isHeldByCurrentThread " + false + " thread id " + threadId);
        }
    }

    private String getMethodSignature(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methName = signature.getMethod().getName();
        String returnType = signature.getMethod().getReturnType().getTypeName();
        return MessageFormat.format(METHOD_SIGNATURE, methName, returnType);
    }
}
