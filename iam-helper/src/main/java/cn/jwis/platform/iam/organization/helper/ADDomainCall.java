package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.iam.organization.dto.ADDomainRefreshDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.*;
import java.util.*;

/**
 * AD域集成服务
 */
@Service
@Slf4j
public class ADDomainCall {

    @Value("${adDomain.userName}")
    private String adDomainUserName;

    @Value("${adDomain.password}")
    private String adDomainPassword;

    @Value("${adDomain.host}")
    private String adDomainHost;

    @Value("${adDomain.domain}")
    private String adDomainDomain;

    @Value("${adDomain.port}")
    private String adDomainPort;

    @Value("${adDomain.searchBase}")
    private String adDomainSearchBase;

//    public static void main(String[] args) throws NamingException {
//        ADDomainCall call = new ADDomainCall();
//        List<ADDomainRefreshDTO> list = call.queryAllUser();
//        int i = 0;
//    }

    public List<ADDomainRefreshDTO> queryAllUser() {
        // 获取ldap目录上下文
        DirContext ctx = callLdap();
        // 向ldap查询数据
        List<Map<String,String>> tempData = searchLdapData(ctx);
        // 处理ldap的数据
        return dealLdapData(tempData);
    }

    public DirContext callLdap(String... userNamePassword) {
        boolean normalUserAuthentic = userNamePassword != null && userNamePassword.length > 1;

        String userName = normalUserAuthentic ? userNamePassword[0] : this.adDomainUserName;//AD域认证，用户的登录UserName
        String password = normalUserAuthentic ? userNamePassword[1] : this.adDomainPassword;//AD域认证，用户的登录PassWord
        String host = this.adDomainHost;//AD域IP，必须填写正确
        String domain = this.adDomainDomain;//域名后缀，例<EMAIL>
        String port = adDomainPort; //端口，一般默认389
        String url = new String("ldap://" + host + ":" + port);//固定写法
        String user = userName.indexOf(domain) > 0 ? userName : userName+ domain;
        Hashtable env = new Hashtable();//实例化一个Env
        DirContext ctx = null;
        env.put(Context.SECURITY_AUTHENTICATION, "simple");//LDAP访问安全级别(none,simple,strong),一种模式，这么写就行
        env.put(Context.SECURITY_PRINCIPAL, user); //用户名
        env.put(Context.SECURITY_CREDENTIALS, password);//密码
        env.put("com.sun.jndi.ldap.read.timeout", "3000");
        env.put(Context.INITIAL_CONTEXT_FACTORY,
                "com.sun.jndi.ldap.LdapCtxFactory");// LDAP工厂类
        env.put(Context.PROVIDER_URL, url);//Url
        try {
            ctx = new InitialDirContext(env);// 初始化上下文
        } catch (Exception e) {
            log.error("ldap call error 1:",e);
            throw new JWIException("连接ldap服务器失败：" + e.getLocalizedMessage());
        }
        return ctx;
    }

    private List<ADDomainRefreshDTO> dealLdapData(List<Map<String, String>> tempData) {
        // 开始处理从Ldap获取到的数据
        List<ADDomainRefreshDTO> userList = new ArrayList<>();
        for (int i = 0; i < tempData.size(); i++) {
            Map<String, String> ldapRow = tempData.get(i);
            // 账号为空则不处理
            if(!validLdapData(ldapRow)){
                continue;
            }
            ADDomainRefreshDTO iamUserData = new ADDomainRefreshDTO();
            iamUserData.setAssociatedAccount(ldapRow.getOrDefault("sAMAccountName",""));
            iamUserData.setNumber(ldapRow.getOrDefault("employeeID",""));
            iamUserData.setName(ldapRow.getOrDefault("cn",""));
            iamUserData.setDisplayName(StringUtils.isBlank(iamUserData.getName()) ? ldapRow.getOrDefault("displayName","") : iamUserData.getName());
            iamUserData.setEmail(ldapRow.getOrDefault("mail",""));
            iamUserData.setPhone(ldapRow.getOrDefault("telephoneNumber",""));
            initOrgData(iamUserData,ldapRow.getOrDefault("distinguishedName",""));
            userList.add(iamUserData);
        }
        log.info("ldap get All row==>" + JSON.toJSONString(tempData));
        log.info("ldap get All User:" + JSON.toJSONString(userList));
        return userList;
    }

    private List<Map<String,String>> searchLdapData(DirContext ctx){
        List<Map<String,String>> tempData = new ArrayList<>();
        // 域节点
        String searchBase = this.adDomainSearchBase;
        // LDAP搜索过滤器类
        // 查询用户，同时可以添加自己需要的查询条件，*代表模糊查询，跟sql中like一样
        String searchFilter = "(&(objectCategory=person)(cn=*))";
        // 创建搜索控制器
        SearchControls searchCtls = new SearchControls();
        // 设置搜索等级
        searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE); // Specify
        //displayName名称(name)，sAMAccountName登录名称(id)，Department部门，Mail邮箱
        //String returnedAtts[] = { "displayName", "sAMAccountName","distinguishedName","Department","Mail","telephoneNumber"};
        //searchCtls.setReturningAttributes(returnedAtts); // 设置返回属性集
        try {
            // 根据设置的域节点、过滤器类和搜索控制器搜索LDAP得到结果
            NamingEnumeration answer = ctx.search(searchBase, searchFilter, searchCtls);// 根据过滤条件查找数据
            while (answer.hasMoreElements()) {// 遍历结果集
                SearchResult sr = (SearchResult) answer.next();
                Attributes Attrs = sr.getAttributes();// 得到域用户属性集
                if (Attrs != null) {
                    try {
                        Map<String, String> m = new HashMap();
                        for (NamingEnumeration ne = Attrs.getAll(); ne.hasMore(); ) {
                            Attribute Attr = (Attribute) ne.next();// 得到下一个属性
                            String key = Attr.getID().toString();
                            // 读取属性值
                            for (NamingEnumeration e = Attr.getAll(); e.hasMore(); ) {
                                String value = e.next().toString();
                                m.put(key, value);
                            }

                        }
                        tempData.add(m);
                    } catch (NamingException e) {
                        log.error("ldap deal row data error:", e);
                        throw new JWIException("ldap行数据解析失败：" + e.getLocalizedMessage());
                    }
                }
                ctx.close();
            }
        }catch (Exception e){
            log.error("ldap search error:",e);
            throw new JWIException("ldap查询失败：" + e.getLocalizedMessage());
        } finally{
            if(null!=ctx){
                try {
                    ctx.close();
                    ctx=null;
                } catch (Exception e) {
                    log.error("ldap call error 2:",e);
                }
            }
        }
        return tempData;
    }


    private boolean validLdapData(Map<String, String> ldapRow){
        String account = ldapRow.get("sAMAccountName");
        String distinguishedName = ldapRow.get("distinguishedName");
        String employeeID = ldapRow.get("employeeID");
        String phone = ldapRow.get("telephoneNumber");
        String email = ldapRow.get("mail");
        return StringUtil.isNotBlank(account)
                && StringUtil.isNotBlank(distinguishedName)
                && StringUtil.isNotBlank(phone)
                && StringUtil.isNotBlank(email)
                && StringUtil.isNotBlank(employeeID);
    }


    private void initOrgData(ADDomainRefreshDTO iamUserData, String distinguishedName) {
        if(StringUtil.isBlank(distinguishedName)){
            return;
        }
        //CN=张欢,OU=运维部,OU=信息化,OU=yinhe_all,DC=test,DC=local
        String[] allUnitArr = distinguishedName.split(",");
        List<String> orgUnitList = new ArrayList<>();
        for(int i = allUnitArr.length-1 ; i >= 0 ; i--){
            String unit = allUnitArr[i];
            String[] unitParts = unit.split("=");
            if("OU".equals(unitParts[0])){
                orgUnitList.add(unitParts[1]);
            }
        }
        // 第一个是公司
        iamUserData.setCompanyName(orgUnitList.remove(0));
        iamUserData.setCompanyCode(iamUserData.getCompanyName());
        // 剩下的是部门链
        iamUserData.setDepartment(String.join(";",orgUnitList));
        iamUserData.setDepCode(orgUnitList);
    }

}