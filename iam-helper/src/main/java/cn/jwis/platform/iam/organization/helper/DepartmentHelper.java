package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.organization.dto.DepartmentCreateDTO;
import cn.jwis.platform.iam.organization.dto.DepartmentUpdateDTO;
import cn.jwis.platform.iam.organization.dto.FuzzyDeptPageByOrgDTO;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.response.DepartmentWithSubscribe;

import java.util.List;

/**
 * <AUTHOR> yefei
 */
public interface DepartmentHelper {
    Department create(DepartmentCreateDTO dto);

    Department update(DepartmentUpdateDTO dto);

    Long delete(String oid,String type);

    List<Department> fuzzy(String searchKey);

    PageResult<Department> fuzzyPage(PageSimpleDTO dto);

    /**
     * 查询部门清单包含订阅信息
     * @param dto
     * @return
     */
    PageResult<DepartmentWithSubscribe> fuzzyPageWithSubscribe(FuzzyDeptPageByOrgDTO dto);

    Department findByOid(String oid);

    Department findByNumber(String number);

    Condition buildSearchCondition(String searchKey);
}
