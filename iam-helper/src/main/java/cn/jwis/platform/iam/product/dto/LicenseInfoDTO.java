package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class LicenseInfoDTO {

    @NotBlank
    @Description("license文件oid")
    private String licenseOid;

    @NotBlank
    @Description("公钥文件oid")
    private String publicKeyOid;

    public LicenseInfoDTO(String licenseOid, String publicKeyOid) {
        this.licenseOid = licenseOid;
        this.publicKeyOid = publicKeyOid;
    }
}
