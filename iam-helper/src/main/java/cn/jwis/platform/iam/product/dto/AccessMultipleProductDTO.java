package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/22
 * @Description :人员一次授权多个应用
 */
@Data
public class AccessMultipleProductDTO {

    @NotBlank
    @Description("人员Oid")
    private String personnelOid;

    @NotEmpty
    @Description("应用Oid清单")
    private Set<String> productOidList;
}
