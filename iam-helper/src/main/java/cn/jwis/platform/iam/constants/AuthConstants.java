package cn.jwis.platform.iam.constants;

/**
 * 认证相关常量
 * <AUTHOR>
 */
public interface AuthConstants {


    String JSON_WEB_TOKEN_KEY_NAME = "Jwi_Json_Web_Token";
    /**
     * 密码上次更新时间
     */
    String JWI_PASSWORD_UPDATE_TIME = "Jwi_Password_UpdateTime";
    /**
     * 密码输入错误次数
     */
    String JWI_PASSWORD_ERRORS_NUMBER = "Jwi_Password_Errors_Number";
    /**
     * 锁定账号
     */
    String JWI_ACCOUNT_LOCK = "Jwi_Account_Lock";

    /**
     * 移动端登录后缀
     */
    String ACCOUNT_MOBILE_SUFFIX = "_mobile";
}
