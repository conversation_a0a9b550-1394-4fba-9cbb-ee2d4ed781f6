package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.annotation.Description;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/21
 * @Description :人员添加当前部门同类型任意岗位参数dto
 */
@Data
@ApiModel
public class PersonnelAddAnyDTO {

    @NotEmpty
    @Description("人员oid")
    private String personnelOid;

    @NotBlank
    @Description("岗位定义oid")
    private String positionNumber;

    @Description("部门oid")
    private String orgOid;
}
