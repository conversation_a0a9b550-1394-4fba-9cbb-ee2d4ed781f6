package cn.jwis.platform.iam.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class CompanyUpdateDTO {

    @NotBlank
    @ApiModelProperty("公司OID")
    private String oid;

    @ApiModelProperty("公司图标")
    private String companyIcon;

    @NotBlank
    @ApiModelProperty("公司名称")
    private String name;

    @ApiModelProperty("公司显示名称")
    private String displayName;

    @ApiModelProperty("公司简称")
    private String shortName;

    @ApiModelProperty("公司描述")
    private String description;

    @ApiModelProperty("公司地址")
    private String addr;

    @ApiModelProperty("公司电话")
    private String phone;

    @ApiModelProperty("公司邮箱")
    private String email;

}
