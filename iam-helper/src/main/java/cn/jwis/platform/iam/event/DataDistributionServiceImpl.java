package cn.jwis.platform.iam.event;

import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.msgqueue.client.message.builder.PublisherBuilder;
import cn.jwis.framework.msgqueue.client.message.definition.event.EventDTO;
import cn.jwis.framework.msgqueue.client.message.definition.event.EventDataContentType;
import cn.jwis.framework.msgqueue.client.message.definition.event.EventDomain;
import cn.jwis.framework.msgqueue.client.message.definition.publisher.MessagePublisher;
import cn.jwis.framework.msgqueue.client.message.invoke.inter.RemoteMsgqueueService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2022/11/25
 * @Description :
 */

@Service
@Slf4j
public class DataDistributionServiceImpl implements DataDistributionService {


    @Value("${mq.mqmessageserviceoid}")
    private String mqMessageServiceoid;

    @Value("${mq.vhname}")
    private String vhName;

    @Value("${event.domain}")
    private String eventDomain;


    @Autowired
    private RemoteMsgqueueService remoteMsgqueueService;

    @Override
    public boolean sendDataToMq(String appKey, String opType, String subject, JSONObject jsonObj) {
        Assert.notNull(appKey, "appKey不能为空!");
        Assert.notNull(opType, "操作类型不能为空!");
        Assert.notNull(subject, "数据主题不能为空!");
        Assert.isTrue(!jsonObj.isEmpty(), "消息内容不能为空!");
        String exName = this.eventDomain + "." + opType;
        String routingKey = exName + "." + appKey;
        PublisherBuilder builder = new PublisherBuilder();
        MessagePublisher publisher = builder.withInstanceOid(this.mqMessageServiceoid).withVhName(this.vhName).withExName(exName).withRoutingKey(routingKey).build();
        EventDTO event = buildEventDTO(opType, subject, jsonObj);
        publisher.setPlayload(event);
        log.info("DataDistributionServiceImpl sendDataToMq mqMessageServiceOid : {} ,vhName : {} ,exName : {} ,routingKey : {}"
                , this.mqMessageServiceoid, this.vhName, exName, routingKey);
        log.info("DataDistributionServiceImpl sendDataToMq event {}", JSONObject.toJSONString(event));
        return this.remoteMsgqueueService.publishMessageWithFeign(publisher);
    }


    private EventDTO buildEventDTO(String opType, String subject, JSONObject jsonObj) {
        EventDTO event = new EventDTO();
        event.setContentType(EventDataContentType.JSON);
        // 此处的domain先使用pdm
        event.setDomain(EventDomain.valueOf("PDM"));
        event.setType(opType);
        event.setSubject(subject);
        event.setData(jsonObj);
        return event;
    }
}
