package cn.jwis.platform.iam.personnel.helper;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.personnel.dto.*;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.response.PersonnelSimpleInfo;
import cn.jwis.platform.iam.personnel.response.PersonnelWithUser;
import cn.jwis.platform.iam.personnel.response.UserWithPosition;
import cn.jwis.platform.iam.response.LevelNodePersonnel;
import cn.jwis.platform.iam.user.User;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yefei
 */
public interface PersonnelHelper {

    /**
     *  创建人员，并绑定岗位，岗位必须为空岗位 根据设置选择是否创建账号。
     *
     * @param dto 创建人员并绑定岗位dto
     * @return 创建完成的人员
     */
    Personnel create(PersonnelCreateDTO dto);

    /**
     * 添加人员到指定的具体岗位实例
     * @param dto 岗位与人员信息
     * @return 岗位实例信息
     */
    Personnel addTo(PersonnelAddAnyDTO dto);

    /**
     * 绑定人员与用户
     *
     * @param personnel 人员
     * @param user 用户
     * @return 绑定后人员
     */
    Personnel bindPersonnelWithUser(Personnel personnel, User user);


    /**
     * 添加人员到当前部门指定岗位类型下的任意岗位实例
     * @param dto 岗位定义,部门，人员信息
     * @return 岗位实例信息
     */
    Personnel addToAny(PersonnelAddAnyDTO dto);

    Personnel update(PersonnelUpdateDTO dto,boolean fromController);

    Long delete(String oid);

    List<LevelNodePersonnel> fuzzy(String searchKey);

    PageResult<Personnel> fuzzyPage(PageSimpleDTO dto);

    PageResult<Personnel> fuzzyPageNoAccount(PageSimpleDTO dto);

    PersonnelWithUser findByOid(String oid);

    Personnel findByNumber(String number);

    List<PersonnelSimpleInfo> fuzzyByOrg(String orgType, String orgOid, boolean invalidFlag, String searchKey);

    List<Personnel> findPersonnelByOrg();

    /**
     *
     * 模糊分页查询部门或公司下人员
     * @param dto 分页模糊信息 validFlag 有效与否查询类型
     * @return 人员信息
     */
    PageResult<PersonnelSimpleInfo> fuzzyPageByOrg(FuzzyPersonnelPageByOrgDTO dto);

    /**
     * 查询当前部门下人员清单
     *
     * @param departmentOidList 部门oid
     * @param invalidFlag       有效性标识
     * @param filterHasAccount  过滤账号
     * @return 查询结果
     */
    List<String> queryByOrgList(List<String> departmentOidList, boolean invalidFlag, boolean filterHasAccount);

    Long cntByOrg(String orgType, String orgOid, String searchKey);

    List<PositionDef> findPosition(String orgOid, String oid);

    PageResult<Personnel> findPersonnelPageByPosition(PersonnelOnPositionPageDTO dto);

    /**
     * 无效化成员
     * @param dto Post参数
     * @return 人员数据
     */
    Personnel invalid(PersonnelInvalidDTO dto);

    Condition buildSearchCondition(String searchKey, Boolean invalidFlag);

    List<PersonnelSimpleInfo> findPositions(List<String> userOidList);

    List<Position> findPositions(Personnel personnel);

    /**
     * 根据
     * @param userOidList 用户oid清单
     * @return 人员清单
     */
    List<Personnel> findPersonnelByUserOidList(List<String> userOidList);

    /**
     * 根据人员id清单查询用户级所属岗位信息
     *
     * @param personnelOidList 用户oid清单
     * @return 查询结果
     */
    List<UserWithPosition>  queryUserWithPositionByPersonnelOidList(List<String> personnelOidList);

    /**
     * 修改用户备注级描述
     *
     * @param dto 备注人的 备注级描述
     * @return 修改结果
     */
    boolean setUserComments(UserDescriptionDTO dto);

    /**
     * 查询并填充用户备注信息
     * @param userIdMap
     */
    void findAndSetUserDescription(Map<String,LevelNodePersonnel> userIdMap);

    List<Personnel> querySubPersonnelByOrgs(List<String> departmentOidList, boolean invalidFlag, boolean filterHasAccount);
}
