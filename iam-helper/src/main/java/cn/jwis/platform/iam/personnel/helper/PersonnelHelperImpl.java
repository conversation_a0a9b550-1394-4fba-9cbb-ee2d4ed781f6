package cn.jwis.platform.iam.personnel.helper;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.iam.account.hepler.IamAccountHelper;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.common.help.MakeTreeHelper;
import cn.jwis.platform.iam.constants.PersonnelSearchType;
import cn.jwis.platform.iam.enums.AccountRuleType;
import cn.jwis.platform.iam.event.NodeChange;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.organization.PositionService;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.organization.helper.CompanyHelper;
import cn.jwis.platform.iam.personnel.PersonnelService;
import cn.jwis.platform.iam.personnel.dto.FuzzyPersonnelPageByOrgDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelAddAnyDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelCreateDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelInvalidDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelOnPositionPageDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelUpdateDTO;
import cn.jwis.platform.iam.personnel.dto.UserDescriptionDTO;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.entity.UserDescription;
import cn.jwis.platform.iam.personnel.response.PersonnelSimpleInfo;
import cn.jwis.platform.iam.personnel.response.PersonnelWithUser;
import cn.jwis.platform.iam.personnel.response.UserWithPosition;
import cn.jwis.platform.iam.product.helper.ProductHelper;
import cn.jwis.platform.iam.relation.AccessTo;
import cn.jwis.platform.iam.relation.BelongTo;
import cn.jwis.platform.iam.relation.InValidTo;
import cn.jwis.platform.iam.relation.LinkTo;
import cn.jwis.platform.iam.response.LevelNodePersonnel;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.iam.user.UserService;
import cn.jwis.platform.iam.user.helper.UserHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 */
@Service
@Transactional
public class PersonnelHelperImpl implements PersonnelHelper {

    protected static final Class<Personnel> POJO_CLASS = Personnel.class;

    private static final Logger logger = LoggerFactory.getLogger(PersonnelHelperImpl.class);

    @Resource
    PersonnelService personnelService;

    @Resource
    PositionService positionService;

    @Resource
    CompanyHelper companyHelper;

    @Resource
    ProductHelper productHelper;

    @Resource
    MakeTreeHelper makeTreeHelper;

    @Resource
    CommonService commonService;

    @Resource
    CommonAbilityHelper commonAbilityHelper;

    @Resource
    CommonAbilityService commonAbilityService;

    @Resource
    IamAccountHelper iamAccountHelper;

    @Resource
    UserService userService;

    @Resource
    UserHelper userHelper;

    @Override
    @NodeChange(action = "create")
    @RestLock
    public Personnel create(PersonnelCreateDTO dto) {
        ValidationUtil.validate(dto);
        // POC需求 账号导入不再按照账号生成规则进行生成
//        AccountRule accountRule = iamAccountHelper.queryAccountRule();
//        Assert.notNull(accountRule, "account.rule.not.blank");
        // 工号去重
        Assert.isNull(findByNumber(dto.getNumber()), dto.getNumber()+":工号已存在");
        // 邮箱去重
        Assert.isNull(findByNumber(dto.getEmail()), dto.getEmail()+":邮箱已存在");
        Personnel personnel = BeanUtil.copyProperties(dto, new Personnel());
        commonAbilityHelper.doCreate(personnel);
        // POC需求自动进行账号生成
        // 需要自动生成账号 则进行生成
        handleAccount(personnel);
        // 岗位是否存在
        Position position = findBindPosition(dto.getPositionNumber(), dto.getOrgOid());
        bindPersonnelOnPosition(personnel, position);
        return personnel;
    }

    private Position findBindPosition(String positionId, String orgId) {
        Position position = commonService.findByOid(Position.TYPE, positionId, Position.class);
        if (ObjectUtils.isEmpty(position)) {
            PositionDef positionDef = commonService.findByOid(PositionDef.TYPE, positionId,
                    PositionDef.class);
            Assert.notNull(positionDef, "position.not.exits");
            //查询当前部门下此类型岗位清单
            List<Position> positionList = positionService.findNoBindPositionByDef(orgId, positionDef.getOid());
            Assert.notEmpty(positionList, "position.not.empty.to.bind");
            position = positionList.stream().findAny().get();
        } else {
            FromToFilter personFilter = new FromToFilter();
            personFilter.setFromType(Personnel.TYPE);
            personFilter.setType(BelongTo.TYPE);
            personFilter.setToType(Position.TYPE);
            personFilter.setToFilter(Condition.where("oid").eq(position.getOid()));
            List<Personnel> personnelList = commonService.dynamicQueryFrom(personFilter, Personnel.class);
            Assert.isEmpty(personnelList, "position.has.bind.personnel");
        }
        return position;
    }

    private void handleAccount(Personnel personnel) {
        User user = iamAccountHelper.generateAccountByPersonnel(personnel);
        Assert.notNull(user, "user.create.fail");
        commonService.createRelation(new LinkTo(User.TYPE, user.getOid(), Personnel.TYPE,
                personnel.getOid()));
        personnel.setAssociatedAccount(user.getAccount());
        commonAbilityHelper.doUpdate(personnel);
    }

    private boolean isUserDefine(int ruleTpe) {
        return AccountRuleType.TYPE_USER_DEFINE.getType() == ruleTpe;
    }

    @Override
    @RestLock
    @NodeChange(action = "create")
    public Personnel addTo(PersonnelAddAnyDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notBlank(dto.getPositionNumber(), "position.oid.not.blank");
        Assert.notBlank(dto.getPersonnelOid(), "personnel.oid.not.blank");

        Personnel personnel = findByOid(dto.getPersonnelOid());
        Assert.notNull(personnel, "position.not.exits");
        Position position = findBindPosition(dto.getPositionNumber(), dto.getOrgOid());
        bindPersonnelOnPosition(personnel, position);
        return personnel;
    }

    @Override
    @NodeChange(action = "update")
    public Personnel bindPersonnelWithUser(Personnel personnel, User user) {
        personnel.setAssociatedAccount(user.getAccount());
        commonService.createRelation(new LinkTo(User.TYPE, user.getOid(), Personnel.TYPE, personnel.getOid()));
        commonAbilityHelper.doUpdate(personnel);
        return personnel;
    }

    @Override
    @RestLock
    @NodeChange(action = "create")
    public Personnel addToAny(PersonnelAddAnyDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notBlank(dto.getPositionNumber(), "position.oid.not.blank");
        Assert.notBlank(dto.getPersonnelOid(), "personnel.oid.not.blank");
        Assert.notBlank(dto.getOrgOid(), "common.org.not.find");

        Personnel personnel = findByOid(dto.getPersonnelOid());
        Assert.notNull(personnel, "personnel.not.exits");
        Position position = findBindPosition(dto.getPositionNumber(), dto.getOrgOid());
        bindPersonnelOnPosition(personnel, position);
        return personnel;
    }

    private Personnel bindPersonnelOnPosition(Personnel personnel, Position bindPosition) {
        List<String> personnelOidList = queryByOrgList(Lists.newArrayList(bindPosition.getParentOid()), false, false);
        Assert.isFalse(personnelOidList.contains(personnel.getOid()), "personnel.in.department.repeat");
        commonService.createRelation(new BelongTo(Personnel.TYPE, personnel.getOid(), Position.TYPE,
                bindPosition.getOid()));
        personnel.setPositionOid(bindPosition.getOid());
        return personnel;
    }

    @Override
    @NodeChange(action = "update")
    public Personnel update(PersonnelUpdateDTO dto, boolean fromController) {
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        Personnel byOid = commonService.findByOid(Personnel.TYPE, oid, Personnel.class);
        Assert.notNull(byOid, "personnel.not.exits");
        BeanUtil.copyProperties(dto, byOid);
        if (fromController && StringUtils.isNotBlank(byOid.getAssociatedAccount())) {
            userHelper.updateWhenPersonnelModify(byOid.getAssociatedAccount(), byOid.getEmail(), byOid.getPhone(),
                    byOid.getName());
        }
        return commonAbilityHelper.doUpdate(byOid);
    }

    @Override
    public Long delete(String oid) {
        throw new JWIServiceException("此功能暂未开放");
    }

    @Override
    public List<LevelNodePersonnel> fuzzy(String searchKey) {
        List<PersonnelSimpleInfo> personnelSimpleInfoList = personnelService.fuzzy(buildSearchCondition(searchKey,
                false),
                buildOrder()).stream().filter(item -> StringUtils.isNotBlank(item.getAssociatedAccount())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(personnelSimpleInfoList)) {
            return Lists.newArrayList();
        }
        // 转换为以人员Oid为Key 人员所属岗位信息列表为value
        Map<String, List<String>> currentIdPerListMap =
                findPerTreeNodeList(personnelSimpleInfoList.stream().map(PersonnelSimpleInfo::getPersonnelOid).collect(Collectors.toList()));
        Map<String, Map<String, LinkedList<String>>> result = makeTreeHelper.findNodePath(currentIdPerListMap);
        List<LevelNodePersonnel> levelNodePersonnelList = personnelSimpleInfoList.stream().map(item -> {
            LevelNodePersonnel levelNodePersonnel = BeanUtil.copyProperties(item, new LevelNodePersonnel());
            levelNodePersonnel.setContactsOid(item.getPersonnelOid());
            levelNodePersonnel.setAccount(item.getAssociatedAccount());
            setLevelPersonnelPosAndPath(levelNodePersonnel,result);
            return levelNodePersonnel;
        }).collect(Collectors.toList());
        findAndSetUserDescription(levelNodePersonnelList.stream().collect(Collectors.toMap(key -> key.getUserOid(),
                value -> value)));
        return levelNodePersonnelList;
    }

    @Override
    public void findAndSetUserDescription(Map<String,LevelNodePersonnel> userIdMap) {
        String currentUserOid = SessionHelper.getCurrentUser().getOid();
        if(org.apache.commons.lang3.StringUtils.isBlank(currentUserOid)) {
            logger.info("query UserDescription currentUserOid blank cancel ");
        }
        Map<String, UserDescription> userDescriptionMap = commonService.dynamicQuery(UserDescription.TYPE,
                Condition.where("fromOid").eq(currentUserOid).and(Condition.where("toOid").in(userIdMap.keySet())),
                UserDescription.class).stream().collect(Collectors.toMap(key -> key.getToOid(), t -> t));

        userIdMap.entrySet().forEach(item->{
            if(userDescriptionMap.containsKey(item.getKey())) {
                item.getValue().setComments(userDescriptionMap.get(item.getKey()).getComments());
                item.getValue().setDescription(userDescriptionMap.get(item.getKey()).getDescription());
            }
        });
    }

    @Override
    public List<Personnel> querySubPersonnelByOrgs(List<String> orgOidList, boolean invalidFlag, boolean filterHasAccount) {
        if (filterHasAccount) {
            return personnelService.querySubPersonnelByOrgs(orgOidList,
                    Condition.where("associatedAccount").neq(StringUtils.EMPTY).and(Condition.where("invalidFlag").eq(invalidFlag)));
        } else {
            return personnelService.querySubPersonnelByOrgs(orgOidList, buildSearchCondition(StringUtils.EMPTY, invalidFlag));
        }
    }

    private void setLevelPersonnelPosAndPath(LevelNodePersonnel levelNodePersonnel,Map<String, Map<String, LinkedList<String>>> result) {
        if(result.containsKey(levelNodePersonnel.getContactsOid())) {
            Map<String, LinkedList<String>> pathListMap = result.get(levelNodePersonnel.getContactsOid());
            List<List<String>> pathList = new ArrayList<>();
            Set<String> posList = Sets.newHashSet();
            pathListMap.values().forEach(item->{
                posList.add(item.pop());
                pathList.add(item);
            });
            levelNodePersonnel.setPositionName(JSONObject.toJSONString(posList));
            levelNodePersonnel.setDepartmentName(JSONObject.toJSONString(pathList));
        }
    }

    private Map<String, List<String>> findPerTreeNodeList(Collection<String> personnelOidList) {
        return companyHelper.getCurrentIdPerMap().entrySet().stream().filter(item -> personnelOidList.contains(item.getKey())).collect(Collectors.toMap(key -> key.getKey(), value -> value.getValue().stream().map(TreeAbleEx::getParentNodeId).collect(Collectors.toList())));
    }

    @Override
    public PageResult<Personnel> fuzzyPage(PageSimpleDTO dto) {
        Condition condition = buildSearchCondition(dto.getSearchKey(), false);
        return commonService.dynamicQueryPage(Personnel.TYPE, condition, buildOrder(), dto.getIndex(), dto.getSize(),
                POJO_CLASS);
    }

    @Override
    public PageResult<Personnel> fuzzyPageNoAccount(PageSimpleDTO dto) {
        Condition condition =
                Condition.where("associatedAccount").eq(StringUtils.EMPTY).and(Condition.where("invalidFlag").eq(false));
        if (StringUtils.isNotBlank(dto.getSearchKey())) {
            condition.and(buildSearchCondition(dto.getSearchKey()));
        }
        return commonService.dynamicQueryPage(Personnel.TYPE, condition, buildOrder(), dto.getIndex(), dto.getSize(),
                POJO_CLASS);
    }


    @Override
    public PersonnelWithUser findByOid(String oid) {
        PersonnelWithUser personnel = BeanUtil.copyProperties(commonAbilityService.findDetailEntity(oid,
                Personnel.TYPE), new PersonnelWithUser());
        if (StringUtils.isNotBlank(personnel.getAssociatedAccount())) {
            User user = userService.searchByAccount(personnel.getAssociatedAccount());
            if (ObjectUtils.isNotEmpty(user)) {
                personnel.setUserOid(user.getOid());
                personnel.setAvatar(user.getAvatar());
            }
        }
        return personnel;
    }

    @Override
    public Personnel findByNumber(String number) {
        return commonService.dynamicQueryOne(Personnel.TYPE,
                Condition.where("number").eq(number),
                POJO_CLASS);
    }

    @Override
    public List<PersonnelSimpleInfo> fuzzyByOrg(String orgType, String orgOid, boolean invalidFlag, String searchKey) {
        Set<String> parentOidList = findOidByType(orgOid, Sets.newHashSet(Department.TYPE, Company.TYPE));
        parentOidList.add(orgOid);
        FromToFilter positionFilter = new FromToFilter();
        positionFilter.setFromType(Position.TYPE);
        positionFilter.setType(BelongTo.TYPE);
        positionFilter.setToFilter(Condition.where("oid").in(parentOidList));
        List<Position> positionList = commonService.dynamicQueryFrom(positionFilter, Position.class);
        if(CollectionUtils.isEmpty(positionList)) {
            return Lists.newArrayList();
        }

        FromToFilter personnelFilter = new FromToFilter();
        personnelFilter.setFromType(Personnel.TYPE);
        personnelFilter.setType(BelongTo.TYPE);
        personnelFilter.setToType(Position.TYPE);
        personnelFilter.setToFilter(Condition.where("oid").in(positionList.stream().map(Position::getOid).collect(Collectors.toSet())).and(buildSearchCondition(searchKey)));
        List<Personnel> personnelList =
                commonService.dynamicQueryFrom(personnelFilter, Personnel.class).stream().filter(item -> StringUtils.isNotBlank(item.getAssociatedAccount())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(personnelList)) {
            return Lists.newArrayList();
        }
        FromToFilter userFilter = new FromToFilter();
        userFilter.setFromType(User.TYPE);
        userFilter.setType(LinkTo.TYPE);
        userFilter.setToType(Personnel.TYPE);
        userFilter.setToFilter(Condition.where("oid").in(personnelList.stream().map(Personnel::getOid).collect(Collectors.toSet())));
        Map<String,User> userMap =
                commonService.dynamicQueryFrom(userFilter, User.class).stream().collect(Collectors.toMap(key -> key.getAccount(), t -> t));
        List<PersonnelSimpleInfo> result = personnelList.stream().map(item -> {
            PersonnelSimpleInfo simpleInfo = new PersonnelSimpleInfo();
            BeanUtil.copyProperties(item, simpleInfo);
            User user = userMap.get(item.getAssociatedAccount());
            simpleInfo.setPersonnelOid(item.getOid());
            simpleInfo.setUserOid(user.getOid());
            simpleInfo.setAvatar(user.getAvatar());
            return simpleInfo;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<Personnel> findPersonnelByOrg() {

        return personnelService.findPersonnelByOrg();
    }

    @Override
    public PageResult<PersonnelSimpleInfo> fuzzyPageByOrg(FuzzyPersonnelPageByOrgDTO dto) {
        if (ObjectUtils.isEmpty(dto.getSearchType())) {
            dto.setSearchType(PersonnelSearchType.SEARCH_TYPE_VALID);
        }
        Set<String> parentOidList = findOidByType(dto.getOrgOid(), Sets.newHashSet(Department.TYPE, Company.TYPE));
        parentOidList.add(dto.getOrgOid());
        return personnelService.fuzzyPageByOrg(parentOidList.stream().collect(Collectors.toList()),
                buildSearchCondition(dto.getSearchKey()),
                buildOrder(), dto.getIndex(), dto.getSize(), dto.getSearchType());
    }

    @Override
    public List<String> queryByOrgList(List<String> orgOidList, boolean invalidFlag, boolean filterHasAccount) {
        if (filterHasAccount) {
            return personnelService.queryByOrgList(orgOidList,
                    Condition.where("associatedAccount").neq(StringUtils.EMPTY).and(Condition.where("invalidFlag").eq(invalidFlag)));
        } else {
            return personnelService.queryByOrgList(orgOidList, buildSearchCondition(StringUtils.EMPTY, invalidFlag));
        }
    }

    @Override
    public Long cntByOrg(String orgType, String orgOid, String searchKey) {
        Set<String> parentOidList = findOidByType(orgOid, Sets.newHashSet(Position.TYPE));
        if (CollectionUtils.isEmpty(parentOidList)) {
            return 0L;
        }
        FromToFilter filter = new FromToFilter();
        filter.setFromType(Personnel.TYPE);
        filter.setType(BelongTo.TYPE);
        filter.setToType(Position.TYPE);
        filter.setToFilter(Condition.where("oid").in(parentOidList));
        Set<String> personnelOidSet =
                commonService.dynamicQueryFrom(filter, Personnel.class).stream().map(Personnel::getOid).collect(Collectors.toSet());
        return Long.valueOf(personnelOidSet.size());
    }

    private Set<String> findOidByType(String orgId, Set<String> typeSet) {
        Set<String> result = new HashSet<>();
        findChildWithType(orgId, typeSet, companyHelper.getParentIdOrgMap(), result);
        return result;
    }

    private void findChildWithType(String orgId, Set<String> typeSet, Map<String, List<TreeAbleEx>> idOrgMap,
                                   Set<String> result) {
        if (!idOrgMap.containsKey(orgId)) {
            return;
        }
        idOrgMap.get(orgId).forEach(item -> {
            if (typeSet.contains(item.getType())) {
                result.add(item.getCurrentNodeId());
            }
            findChildWithType(item.getCurrentNodeId(), typeSet, idOrgMap, result);
        });
    }

    @Override
    public List<PositionDef> findPosition(String orgOid, String oid) {
        FromToFilter filter = new FromToFilter();
        filter.setFromType(Personnel.TYPE);
        filter.setFromFilter(Condition.where("oid").eq(oid));
        filter.setType(BelongTo.TYPE);
        filter.setToType(Position.TYPE);
        if (StringUtil.isNotBlank(orgOid)) {
            filter.setToFilter(Condition.where("parentOid").eq(orgOid));
        }

        List<Position> positionList = commonService.dynamicQueryTo(filter, Position.class);
        Set<String> positionDfOidSet =
                positionList.stream().map(Position::getPositionDefOid).collect(Collectors.toSet());

        List<PositionDef> result = commonService.findByOid(PositionDef.TYPE, Lists.newArrayList(positionDfOidSet),
                PositionDef.class);

        return result;
    }

    @Override
    public PageResult<Personnel> findPersonnelPageByPosition(PersonnelOnPositionPageDTO dto) {
        return personnelService.findPersonnelPageByPosition(dto.getOrgId(),
                dto.getPositionOid(), buildSearchCondition(dto.getSearchKey(), false),
                buildOrder(), dto.getIndex(), dto.getSize());
    }

    @Override
    @NodeChange(action = "delete")
    public Personnel invalid(PersonnelInvalidDTO dto) {
        String personnelOid = dto.getPersonnelOid();
        Personnel personnel = findByOid(personnelOid);
        Assert.notNull(personnel, "personnel.not.exits");
        personnel.setInvalidFlag(true);
        personnel.setPositionOid(StringUtils.EMPTY);
        commonAbilityHelper.doUpdate(personnel);
        // 删除授权信息
        FromToFilter accessFilter = new FromToFilter();
        accessFilter.setFromType(Personnel.TYPE);
        accessFilter.setFromFilter(Condition.where("oid").eq(personnel.getOid()));
        accessFilter.setType(AccessTo.TYPE);
        commonService.deleteRelation(accessFilter);
        // 查询成员岗位归属关系
        FromToFilter positionFilter = new FromToFilter();
        positionFilter.setFromType(Personnel.TYPE);
        positionFilter.setFromFilter(Condition.where("oid").eq(personnel.getOid()));
        positionFilter.setType(BelongTo.TYPE);
        positionFilter.setToType(Position.TYPE);
        //查询归属部门 并建立失效后与部门的归属关系
        Set<String> positionOidList =
                commonService.dynamicQueryTo(positionFilter, Position.class).stream().map(Position::getOid).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(positionOidList)) {
            FromToFilter belongFilter = new FromToFilter();
            belongFilter.setFromType(Position.TYPE);
            belongFilter.setFromFilter(Condition.where("oid").in(positionOidList));
            belongFilter.setType(BelongTo.TYPE);
            List<BaseEntity> belongOrgList = commonService.dynamicQueryTo(belongFilter, BaseEntity.class);
            commonService.createOutRelation(belongOrgList.stream().map(item -> new InValidTo(Personnel.TYPE,
                    personnel.getOid(),
                    item.getType(), item.getOid())).collect(Collectors.toList()));
        }
        deletePersonFromPosition(personnel.getOid());

        if (StringUtils.isNotBlank(personnel.getAssociatedAccount())) {
            userHelper.disable(userHelper.searchByAccount(personnel.getAssociatedAccount()));
        }
        return personnel;
    }

    public Long deletePersonFromPosition(String oid) {
        FromToFilter belongFilter = new FromToFilter();
        belongFilter.setFromType(Personnel.TYPE);
        belongFilter.setFromFilter(Condition.where("oid").eq(oid));
        belongFilter.setType(BelongTo.TYPE);
        belongFilter.setToType(Position.TYPE);
        return commonService.deleteRelation(belongFilter);
    }

    private Condition buildSearchCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("number").contain(searchKey))
                    .or(Condition.where("associatedAccount").contain(searchKey))
                    .or(Condition.where("displayName").contain(searchKey));
        }
        return condition;
    }

    @Override
    public Condition buildSearchCondition(String searchKey, Boolean invalidFlag) {
        Condition condition = ObjectUtils.isEmpty(invalidFlag) ? null :
                Condition.where("invalidFlag").eq(invalidFlag);
        if (StringUtil.isNotBlank(searchKey)) {
            Condition searchCondition = buildSearchCondition(searchKey);
            if (ObjectUtils.isEmpty(condition)) {
                return searchCondition;
            } else {
                condition.and(searchCondition);
            }
        }
        return condition;
    }

    @Override
    public List<PersonnelSimpleInfo> findPositions(List<String> userOidList) {
        List<PersonnelSimpleInfo> list = new ArrayList<>();
        List<Personnel> personnelList = personnelService.findPersonnelByUserOidList(userOidList);
        personnelList.forEach(personnel -> {
            // 循环查询
            List<Position> positions = findPositions(personnel);
            positions.forEach(position -> {
                PersonnelSimpleInfo personnelSimpleInfo = new PersonnelSimpleInfo();
                personnelSimpleInfo.setPositions(position.getDisplayName());
                personnelSimpleInfo.setAssociatedAccount(personnel.getAssociatedAccount());
                personnelSimpleInfo.setPersonnelOid(personnel.getOid());
                list.add(personnelSimpleInfo);
            });
        });
        return list;

    }

    @Override
    public List<Position> findPositions(Personnel personnel) {
        if (Objects.isNull(personnel)) {
            return Collections.emptyList();
        } else {
            FromToFilter fromToFilter = new FromToFilter();
            fromToFilter.setFromFilter(Condition.where("oid").eq(personnel.getOid()));
            fromToFilter.setType(BelongTo.TYPE);
            fromToFilter.setFromType(Personnel.TYPE);
            fromToFilter.setToType(Position.TYPE);
            return commonService.dynamicQueryTo(fromToFilter, Position.class);
        }
    }

    @Override
    public List<Personnel> findPersonnelByUserOidList(List<String> userOidList) {
        return personnelService.findPersonnelByUserOidList(userOidList);
    }

    @Override
    public List<UserWithPosition> queryUserWithPositionByPersonnelOidList(List<String> personnelOidList) {
        return personnelService.queryUserWithPositionByPersonnelOidList(personnelOidList);
    }

    @Override
    public boolean setUserComments(UserDescriptionDTO dto) {
        String currentUserOid = SessionHelper.getCurrentUser().getOid();
        logger.info("setUserComments currentUserOid {}", currentUserOid);
        Assert.notBlank(currentUserOid,"user.not.blank.oid");
        User targetUser = commonService.findByOid(User.TYPE,dto.getUserOid(),User.class);
        Assert.notNull(targetUser,"user.target.not.exists");
        Condition condition =
                Condition.where("fromOid").eq(currentUserOid).and(Condition.where("toOid").eq(dto.getUserOid()));
        UserDescription nowObject =  commonService.dynamicQueryOne(UserDescription.TYPE,condition,
                UserDescription.class);
        if(ObjectUtils.isEmpty(nowObject)) {
            UserDescription userDescription = new UserDescription();
            userDescription.setFromOid(currentUserOid);
            userDescription.setToOid(targetUser.getOid());
            userDescription.setComments(dto.getComments());
            userDescription.setDescription(dto.getDescription());
            userDescription.setOid(OidGenerator.newOid());
            commonService.create(userDescription);
        } else {
            nowObject.setComments(dto.getComments());
            nowObject.setDescription(dto.getDescription());
            commonService.update(nowObject);
        }
        return true;
    }

    private Order buildOrder() {
        return new Order().desc("invalidFlag").desc("name");
    }
}
