package cn.jwis.platform.iam.common.help;


import cn.jwis.platform.iam.organization.helper.CompanyHelper;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.structure.TreeNode;
import com.alibaba.excel.util.StringUtils;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 建树用工具
 */
@Service
public class MakeTreeHelper {

    @Resource
    CompanyHelper companyHelper;

    public TreeNode makeOrgTree(Collection<TreeAbleEx> treeAbleExList) {
        if (CollectionUtils.isEmpty(treeAbleExList)) {
            return null;
        }
        Map<String, TreeNode> treeNodeOidMap = new HashMap<>(treeAbleExList.size());

        treeAbleExList.forEach(item -> {
            TreeNode parentNode = treeNodeOidMap.get(item.getParentNodeId());
            if (parentNode == null) {
                parentNode = new TreeNode();
                treeNodeOidMap.put(item.getParentNodeId(), parentNode);
            }

            TreeNode currentNode = treeNodeOidMap.get(item.getCurrentNodeId());
            if (currentNode == null) {
                currentNode = new TreeNode();
                treeNodeOidMap.put(item.getCurrentNodeId(), currentNode);
            }
            currentNode.setCurrent(item);
            parentNode.addChildren(currentNode);
        });
        return treeNodeOidMap.get(StringUtils.EMPTY).getChildren().first();
    }

    public Map<String, LinkedList<String>> findNodePath(Set<String> nodeOidSet) {
        Map<String, LinkedList<String>> result = Maps.newHashMap();
        Map<String, TreeAbleEx> orgMap = companyHelper.getCurrentIdOrgMap();
        nodeOidSet.forEach(item -> {
            LinkedList<String> path = new LinkedList<>();
            findAndPutPath(item, path, orgMap);
            result.put(item, path);
        });
        return result;
    }

    public Map<String, Map<String, LinkedList<String>>> findNodePath(Map<String, List<String>> nodeOidMap) {
        Map<String, Map<String, LinkedList<String>>> result = Maps.newHashMap();
        Map<String, TreeAbleEx> orgMap = companyHelper.getCurrentIdOrgMap();
        for (Map.Entry<String, List<String>> entry : nodeOidMap.entrySet()) {
            Map<String, LinkedList<String>> onePathResult = Maps.newHashMap();
            entry.getValue().forEach(item -> {
                LinkedList<String> path = new LinkedList<>();
                findAndPutPath(item, path, orgMap);
                onePathResult.put(item, path);
            });
            result.put(entry.getKey(), onePathResult);
        }
        return result;
    }

    private void findAndPutPath(String currentId, LinkedList<String> pathList, Map<String, TreeAbleEx> orgMap) {
        TreeAbleEx treeAbleEx = orgMap.get(currentId);
        if (!ObjectUtils.isEmpty(treeAbleEx)) {
            pathList.add(treeAbleEx.getDisplayName());
            findAndPutPath(treeAbleEx.getParentNodeId(), pathList, orgMap);
        }
    }

}
