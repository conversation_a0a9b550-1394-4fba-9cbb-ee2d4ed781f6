package cn.jwis.platform.iam.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class PositionUpdateSetupDTO {

    @NotBlank
    @ApiModelProperty("父级类型Company或Department")
    private String fromType;

    @NotBlank
    @ApiModelProperty("父级OID")
    private String fromOid;

    @Min(1)
    @ApiModelProperty("数量")
    private int quantity = 1;

    @ApiModelProperty("汇报岗位的Oid")
    private String reportPositionOid;

    @ApiModelProperty("岗位的oid")
    private String positionOid;

}
