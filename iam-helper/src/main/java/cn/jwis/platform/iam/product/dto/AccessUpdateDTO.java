package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/22
 * @Description :更新应用授权
 */
@Data
public class AccessUpdateDTO {

    @NotEmpty
    @Description("授权oid清单")
    Set<String> oidList;

    @NotBlank
    @Description("应用Oid")
    private String productOid;

    @NotBlank
    @Description("授权开始时间")
    private String activeDate;

    @Description("授权结束时间")
    private String expirationDate;
}
