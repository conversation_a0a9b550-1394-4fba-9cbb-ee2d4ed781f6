package cn.jwis.platform.iam.sysinit;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.config.security.PasswordPolicyService;
import cn.jwis.platform.iam.config.security.entity.PasswordPolicy;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.product.entity.Product;
import cn.jwis.platform.plm.foundation.model.dto.ModelLayoutCreateDTO;
import cn.jwis.platform.plm.foundation.model.dto.ModelLayoutDTO;
import cn.jwis.platform.plm.foundation.model.dto.ModelPropertyAssignDTO;
import cn.jwis.platform.plm.foundation.model.dto.ModelPropertyCreateDto;
import cn.jwis.platform.plm.foundation.model.dto.ModelPropertyCreateInfo;
import cn.jwis.platform.plm.foundation.model.entity.Property;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.related.ModelLayout;
import cn.jwis.platform.plm.foundation.model.service.ModelLayoutHelper;
import cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelper;
import cn.jwis.platform.plm.foundation.site.Site;
import com.alibaba.fastjson.JSONArray;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/23
 * @Description :
 */
@Component
@Transactional
public class SystemDataInitHelper {

    @Resource
    CommonService commonService;

    @Resource
    ModelPropertyHelper modelPropertyHelper;

    @Resource
    ModelLayoutHelper modelLayoutHelper;

    @Resource
    PasswordPolicyService passwordPolicyService;

    private static final List<String> VERTEX_DEF_OBJECT_LIST = new ArrayList<String>(){{
        add(Company.TYPE);
        add(Department.TYPE);
        add(PositionDef.TYPE);
        add(Position.TYPE);
        add(Personnel.TYPE);
        add(Product.TYPE);
    }};

    public boolean createVeDefAndSite() {
        List<VertexDef> vertexDefList = VERTEX_DEF_OBJECT_LIST.stream().map(item->{
            VertexDef vertexDef = new VertexDef();
            vertexDef.setCode(item);
            vertexDef.setName(item);
            vertexDef.setOid(OidGenerator.newOid());
            vertexDef.setColorHex("#c12e34");
            return vertexDef;
        }).collect(Collectors.toList());

        vertexDefList.forEach(item -> commonService.create(item));


        Site site = new Site();
        site.setType(Site.TYPE);
        site.setName("master");
        site.setDescription("系统master站点");
        site.setOid(OidGenerator.newOid());
        commonService.create(site);
        return true;
    }

    public boolean createPasswordPolicy() {
        PasswordPolicy passwordPolicy = new PasswordPolicy();
        passwordPolicy.setAccountLockoutDuration(1L);
        passwordPolicy.setDefaultPassword("Jwi_plm@2022");
        passwordPolicy.setMaximumNumberofIncorrectPasswords(4L);
        passwordPolicy.setMaximumPasswordLength(12L);
        passwordPolicy.setMinimumPasswordLength(1L);
        passwordPolicy.setPasswordComplexityRequirements("大写字母,小写字母,数字");
        passwordPolicy.setPasswordUpdateInterval(180L);
        passwordPolicyService.save(passwordPolicy);
        return true;
    }

    @SneakyThrows
    public boolean createProperty(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        // 读数据
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(file.getInputStream()));
        String str;
        StringBuffer stringBuffer = new StringBuffer();
        while ((str = bufferedReader.readLine()) != null) {
            stringBuffer.append(str);
        }
        String type = originalFilename.split("_")[0];
        String belongOid = commonService.dynamicQueryOne(VertexDef.TYPE, Condition.where("code").eq(type), VertexDef.class).getOid();
        List<Property> propertyList = JSONArray.parseArray(stringBuffer.toString(), Property.class);
        propertyList.forEach(item->{
            ModelPropertyCreateDto dto = new ModelPropertyCreateDto();
            dto.setModelOid(belongOid);
            ModelPropertyCreateInfo propertyCreateInfo = new ModelPropertyCreateInfo();
            BeanUtil.copyProperties(item,propertyCreateInfo);
            dto.setProperty(propertyCreateInfo);

            ModelPropertyAssignDTO modelPropertyAssignDTO = new ModelPropertyAssignDTO();
            modelPropertyAssignDTO.setFromOid(belongOid);
            modelPropertyAssignDTO.setToOid(belongOid);
            modelPropertyAssignDTO.setConstraintType("none");
            dto.setConstraints(modelPropertyAssignDTO);
            modelPropertyHelper.create(dto);
        });
        return true;
    }

    @SneakyThrows
    public boolean createLayout(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        // 读数据
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(file.getInputStream()));
        String str;
        StringBuffer stringBuffer = new StringBuffer();
        while ((str = bufferedReader.readLine()) != null) {
            stringBuffer.append(str);
        }
        String type = originalFilename.split("_")[0];
        String belongOid = commonService.dynamicQueryOne(VertexDef.TYPE, Condition.where("code").eq(type), VertexDef.class).getOid();
        List<ModelLayout> modelLayoutList = JSONArray.parseArray(stringBuffer.toString(), ModelLayout.class);
        modelLayoutList.forEach(item->{
            ModelLayoutCreateDTO modelLayoutCreateDTO = new ModelLayoutCreateDTO();
            ModelLayoutDTO modelLayoutDTO = new ModelLayoutDTO();
            BeanUtil.copyProperties(item,modelLayoutDTO);
            modelLayoutCreateDTO.setLayout(modelLayoutDTO);
            modelLayoutCreateDTO.setOid(belongOid);
            modelLayoutHelper.create(modelLayoutCreateDTO);
        });
        return true;
    }
}
