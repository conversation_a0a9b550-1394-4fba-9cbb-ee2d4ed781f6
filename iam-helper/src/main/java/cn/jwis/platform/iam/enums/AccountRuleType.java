package cn.jwis.platform.iam.enums;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/23
 * @Description :账号规则类型
 */
public enum AccountRuleType {

    TYPE_NUMBER(1,"按照工号规则"),
    TYPE_USER_DEFINE(2,"自定义规则");
    /**
     * 类型
     */
    private int type;

    /**
     * 说明
     */
    private String des;

    AccountRuleType(int type,String des){
        this.type = type;
        this.des = des;
    }

    public int getType() {
        return type;
    }
}
