package cn.jwis.platform.iam.user.helper;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.iam.user.dto.UserDTO;
import cn.jwis.platform.iam.user.dto.UserTokenRefreshDTO;
import cn.jwis.platform.plm.foundation.attachment.entity.File;

import java.util.List;
import java.util.Map;

public interface UserHelper {


    /**
     * 创建用户，并加入对应的租户&组织
     *
     * @param dto
     * @return
     */
    User create(UserDTO dto) throws JWIException;

    /**
     * 账户搜索用户
     *
     * @param account
     * @return
     */
    UserDTO searchByAccount(String account) throws JWIException;


    List<UserDTO> searchByAccounts(List<String> account) throws JWIException;

    /**
     * 更新用户信息
     *
     * @param data
     * @return
     */
    User update(UserDTO data) throws JWIException;

    /**
     * 人员更新时同步更新应用
     *
     * @param account 用户名
     * @param email 邮箱
     * @param phone 电话
     * @param name 名称
     * @return 修改后的user对象
     * @throws JWIException
     */
    User updateWhenPersonnelModify(String account,String email,String phone,String name) throws JWIException;


    /**
     * 失效、生效设置
     *
     * @param userUpdateDTO
     * @return
     * @throws JWIException
     */
    User disable(UserDTO userUpdateDTO) throws JWIException;


    /**
     * 重新设置密码
     *
     * @param userOid
     * @param oldPassword
     * @param confirmPassword
     * @param newPassword
     * @return
     */
    User resetPassword(String userOid, String oldPassword, String confirmPassword, String newPassword) throws JWIException;


    /**
     * 获取用户信息
     *
     * @param userOid
     * @return
     */
    UserDTO searchByOid(String userOid) throws JWIException;


    List<UserDTO> searchByOidList(List<String> oidList);

    /**
     * 初始化密码
     *
     * @param userOid
     * @return
     * @throws JWIException
     */
    UserDTO initPassword(String userOid) throws JWIException;

    /**
     * 校验原始密码是否正确
     *
     * @param dto
     * @return
     */
    boolean validateOldPassword(UserDTO dto);

    /**
     * 密码超期，更新密码接口
     *
     * @param userOid
     * @param oldPassword
     * @param confirmPassword
     * @param newPassword
     * @return
     */
    User passwordCycleUpdate(String userOid, String oldPassword, String confirmPassword, String newPassword);

    void refreshTokenRedis(UserTokenRefreshDTO dto);

    Map<String, File> findSignatureByAccount(List<String> accounts);
}
