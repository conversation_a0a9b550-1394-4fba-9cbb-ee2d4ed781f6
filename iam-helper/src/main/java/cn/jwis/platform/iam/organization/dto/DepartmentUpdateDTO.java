package cn.jwis.platform.iam.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class DepartmentUpdateDTO {

    @ApiModelProperty("部门OID")
    private String oid;

    @NotBlank
    @ApiModelProperty("部门名称 公司下唯一值")
    private String name;

    @ApiModelProperty("部门显示名称")
    private String displayName;

    @ApiModelProperty("部门描述")
    private String description;

    @ApiModelProperty("部门负责人")
    private String deptOwner;

}
