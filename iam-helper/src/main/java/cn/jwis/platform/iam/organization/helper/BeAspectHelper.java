package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.domain.entity.BaseRelationship;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.event.NodeChange;
import cn.jwis.platform.iam.organization.PositionService;
import cn.jwis.platform.iam.organization.dto.CompanyCreateDTO;
import cn.jwis.platform.iam.organization.dto.DepartmentCreateDTO;
import cn.jwis.platform.iam.organization.dto.PositionSetUpDTO;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.relation.BelongTo;
import cn.jwis.platform.iam.relation.LinkTo;
import cn.jwis.platform.iam.response.ImportTreeNode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/24
 * @Description : 用于申明注解识别方法用
 */
@Component
public class BeAspectHelper {

    @Resource
    CommonService commonService;

    @Resource
    PositionService positionService;

    @Resource
    CompanyHelper companyHelper;

    @Resource
    DepartmentHelper departmentHelper;

    @Resource
    PositionHelper positionHelper;

    private static final Logger logger = LoggerFactory.getLogger(CompanyHelperImpl.class);

    @NodeChange(action="create")
    public List<Position> createPositionAndBind(int size, PositionDef positionDef, String fromOid,String fromType,
                                                String reportPositionOid) {
        List<BaseRelationship> shouldBeCreate = new ArrayList<>();

        List<Position> result = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            Position position = new Position();
            position.setOid(OidGenerator.newOid());
            position.setPositionDefOid(positionDef.getOid());
            position.setParentType(fromType);
            position.setParentOid(fromOid);
            position.setDisplayName(positionDef.getDisplayName());
            commonService.create(position);
            result.add(position);

            BelongTo belongTo = new BelongTo(Position.TYPE, position.getOid(), fromType, fromOid);

            shouldBeCreate.add(belongTo);
            if (StringUtil.isNotBlank(reportPositionOid)) {
                position.setReportPositionOid(reportPositionOid);
                LinkTo reportLinkTo = new LinkTo(Position.TYPE, position.getOid(), Position.TYPE, reportPositionOid);
                shouldBeCreate.add(reportLinkTo);
            }
            LinkTo defLinkTo = new LinkTo(Position.TYPE, position.getOid(), PositionDef.TYPE, positionDef.getOid());
            shouldBeCreate.add(defLinkTo);

        }
        commonService.createOutRelation(shouldBeCreate);
        return result;
    }

    @NodeChange(action="update")
    public List<Position> updatePositionReport(String reportPositionOid, List<Position> existsPositionInCurOrg) {
        // 已分配岗位且汇报人不一致进行更新
        List<BaseRelationship> shouldBeCreate = new ArrayList<>();
        List<String> oidList = new ArrayList<>();
        existsPositionInCurOrg.forEach(item -> {
            if (!StringUtil.equalsIgnoreCase(reportPositionOid,
                    item.getReportPositionOid())) {
                oidList.add(item.getOid());
                if (StringUtils.isNotBlank(reportPositionOid)) {
                    shouldBeCreate.add(new LinkTo(Position.TYPE, item.getOid(), Position.TYPE,
                            reportPositionOid));
                }
            }
        });

         if (CollectionUtil.isNotEmpty(oidList)) {
            FromToFilter deleteFilter = new FromToFilter();
            deleteFilter.setFromType(Position.TYPE);
            deleteFilter.setType(LinkTo.TYPE);
            deleteFilter.setFromFilter(Condition.where("oid").in(oidList));
            deleteFilter.setToType(Position.TYPE);
            commonService.deleteRelation(deleteFilter);
            commonService.createOutRelation(shouldBeCreate);
            positionService.updatePositionReportOid(oidList, reportPositionOid);
        }
        return existsPositionInCurOrg;
    }


    @NodeChange(action="delete")
    public Long deletePositionList(List<String> positionList, String type) {
        return commonService.delete(type, positionList);
    }

    @NodeChange(action="update")
    public List<Position> updatePositionDisplayName(String positionDefOid, String displayName) {
        FromToFilter filter = new FromToFilter();
        filter.setFromType(Position.TYPE);
        filter.setType(LinkTo.TYPE);
        filter.setToType(PositionDef.TYPE);
        filter.setToFilter(Condition.where("oid").eq(positionDefOid));
        List<Position> positionList = commonService.dynamicQueryFrom(filter, Position.class);
        if (CollectionUtil.isEmpty(positionList)) {
            return new ArrayList<>();
        }
        positionService.updatePositionDisplayName(positionList.stream().map(Position::getOid).collect(Collectors.toList()), displayName);
        positionList.stream().forEach(item -> {
            item.setDisplayName(displayName);
        });
        return positionList;
    }

    public void createOrgByImportNode(ImportTreeNode importTreeNode, String parentOid, String parentType) {
        logger.info("Import to create Type{},Name{}  and ParentType{} ParentId{}", importTreeNode.getType(),
                importTreeNode.getName(), parentType, parentOid);
        switch (importTreeNode.getType()) {
            case Company.TYPE:
                Company company = companyHelper.create(createCompanyCreateDTO(importTreeNode.getName(),parentOid));
                importTreeNode.setOid(company.getOid());
                break;
            case Department.TYPE:
                logger.info("create department {}", importTreeNode);
                Department department = departmentHelper.create(createDepartmentCreateDTO(importTreeNode.getName(),
                        parentOid,
                        parentType));
                importTreeNode.setOid(department.getOid());
                break;
            case Position.TYPE:
                List<Position> positionList = positionHelper.createSetUp(createPositionSetUpDTO(parentOid, parentType,
                        importTreeNode.getPositionDfOid()), false);
                importTreeNode.setOid(positionList.stream().findFirst().get().getOid());
            default:

        }
    }


    private PositionSetUpDTO createPositionSetUpDTO(String parentOid, String parentType,
                                                    String positionDefOid) {
        PositionSetUpDTO createDTO = new PositionSetUpDTO();
        createDTO.setPositionOid(positionDefOid);
        createDTO.setFromType(parentType);
        createDTO.setFromOid(parentOid);
        createDTO.setQuantity(1);
        return createDTO;
    }

    private DepartmentCreateDTO createDepartmentCreateDTO(String name, String parentOid, String parentType) {
        DepartmentCreateDTO createDTO=  new DepartmentCreateDTO();
        createDTO.setDisplayName(name);
        createDTO.setName(name);
        createDTO.setParentOid(parentOid);
        createDTO.setParentType(parentType);
        return createDTO;
    }

    private CompanyCreateDTO createCompanyCreateDTO(String name, String parentOid) {
        CompanyCreateDTO companyCreateDTO = new CompanyCreateDTO();
        companyCreateDTO.setParentCompanyOid(parentOid);
        companyCreateDTO.setDisplayName(name);
        companyCreateDTO.setName(name);
        companyCreateDTO.setShortName(name);
        return companyCreateDTO;
    }
}
