package cn.jwis.platform.iam.account.hepler;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.iam.account.dto.AccountCreateDTO;
import cn.jwis.platform.iam.account.dto.AccountRuleCreateDTO;
import cn.jwis.platform.iam.account.dto.AccountRuleUpdateDTO;
import cn.jwis.platform.iam.accountrule.AccountRule;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.response.AccountWithPersonnelInfo;
import cn.jwis.platform.iam.personnel.response.EmptyUser;
import cn.jwis.platform.iam.user.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IamAccountHelper {

    /**
     * 创建账号生成规则
     * @param dto
     * @return 账号生成规则
     */
    AccountRule createAccountRule(AccountRuleCreateDTO dto);

    /**
     * 创建账号生成规则
     * @param dto
     * @return 账号生成规则
     */
    AccountRule updateAccountRule(AccountRuleUpdateDTO dto);

    /**
     * 查询是否已存在账号生成规则
     * @return 账号生成规则
     */
    AccountRule queryAccountRule();

    /**
     * 根据规则生成账号
     * @param dto 账号创建参数
     * @return 新账号名称
     */
    User generateAccount(AccountCreateDTO dto);

    /**
     * 根据人员信息生成账号
     * @param personnel 人员信息
     * @return 生成用户
     */
    User generateAccountByPersonnel(Personnel personnel);

    /**
     * 账号人员信息分页模糊查询
     * @param dto 分页模糊参数
     * @return 查询结果
     */
    PageResult<AccountWithPersonnelInfo> fuzzyPage(PageSimpleDTO dto);

    /**
     * 获取自动生成的账号
     *
     * @return 账号名称
     */
    String getNextAccount();

    /**
     * 查询未分配人员账号
     * @return 账号清单
     */
    List<EmptyUser> queryEmptyUser();

    List<User> queryByAccount(List<String> accountList);
}
