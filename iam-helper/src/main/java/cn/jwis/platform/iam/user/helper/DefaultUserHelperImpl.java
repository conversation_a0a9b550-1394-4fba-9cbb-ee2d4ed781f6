package cn.jwis.platform.iam.user.helper;

import cn.jwis.core.base.database.redis.utils.MultiTenantRedisUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.iam.auth.helper.AuthHelper;
import cn.jwis.platform.iam.common.util.BaseEntityUtil;
import cn.jwis.platform.iam.common.util.MD5Util;
import cn.jwis.platform.iam.config.security.PasswordPolicyService;
import cn.jwis.platform.iam.config.security.entity.PasswordPolicy;
import cn.jwis.platform.iam.constants.AuthConstants;
import cn.jwis.platform.iam.event.NodeChange;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.personnel.PersonnelService;
import cn.jwis.platform.iam.personnel.dto.PersonnelUpdateDTO;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.iam.user.UserService;
import cn.jwis.platform.iam.user.dto.UserDTO;
import cn.jwis.platform.iam.user.dto.UserTokenRefreshDTO;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.repo.entity.able.EntityAbleRepo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Transactional
public class DefaultUserHelperImpl implements UserHelper {

    @Value("${login.cookie.timeout.day}")
    String EXPIRE_TIME;

    @Autowired
    UserService userService;


    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    AuthHelper authHelper;

    @Autowired
    EntityAbleRepo entityAbleRepo;


    @Autowired
    private PasswordPolicyService passwordPolicyService;


    @Autowired
    private PersonnelHelper personnelHelper;

    @Autowired
    private PersonnelService personnelService;

    @Override
    @NodeChange(action = "create")
    @RestLock
    public User create(UserDTO dto) {
        // 用户全名 账号 必填 ，手机 邮箱地址非必填
        // Assert.notBlank(dto.getName(), "name.input.null");
        //创建用户需要指定租户、组织Oid
//        Assert.notBlank(dto.getTenantOid(), "user.tenantOid.null");
        Assert.notBlank(dto.getAccount(), "user.account.null");
        Assert.notBlank(dto.getName(), "user.name.null");
        User user = new User();
        BeanUtil.copyProperties(dto, user);
        BaseEntityUtil.setCreateAttrs(user);
        //如果有外部传入的oid则使用传入的oid
        if (StringUtils.isNotEmpty(dto.getOid())) {
            user.setOid(dto.getOid());
        }
        //没有填写密码，设置默认密码
        md5Password(user);
        //校验 账号唯一，邮箱唯一
        List<User> existUser = userService.checkUser(user);
        checkExistUser(existUser, dto, true, true);

        //1、创建用户
        userService.addUser(user);

        return user;
    }

    private StringBuilder checkExistUser(List<User> existUser, UserDTO dto, boolean needThows, boolean checkAccount) {
        if (existUser == null) {
            return new StringBuilder();
        }
        StringBuilder builder = new StringBuilder();
        String paramAccount = dto.getAccount();
        String paramEmail = dto.getEmail();
        for (User user : existUser) {
            String account = user.getAccount();
            String email = user.getEmail();
            if (checkAccount && StringUtils.isNotEmpty(paramAccount) && paramAccount.equals(account)) {
                builder.append("账号重复：" + paramAccount + "；");
            }
            if (StringUtils.isNotEmpty(paramEmail) && paramEmail.equals(email)) {
                builder.append("邮箱重复：" + email + "；");
            }
        }
        if (needThows && builder.length() > 0) {
            throw new JWIException(builder.toString());
        }
        return builder;
    }

    private void md5Password(User user) {
        String password = user.getPassword();
        if (StringUtils.isBlank(password)) {
            PasswordPolicy passwordPolicy = passwordPolicyService.list().stream().findAny().get();
            String defaultPassword = passwordPolicy.getDefaultPassword();
            user.setPassword(MD5Util.generateMD5(defaultPassword));
        } else {
            user.setPassword(MD5Util.generateMD5((user.getPassword())));
        }
    }

    @Override
    public UserDTO searchByAccount(String account) {
        User user = userService.searchByAccount(account);
        if (user == null) {
            return null;
        }
        return BeanUtil.copyProperties(user, new UserDTO());
    }

    @Override
    public List<UserDTO> searchByAccounts(List<String> account) throws JWIException {
        List<User> users = userService.searchByAccounts(account);
        if (CollectionUtil.isEmpty(users)) {
            return new ArrayList<>();
        }
        return BeanUtil.cloneList(users, UserDTO.class);
    }

    @Override
    @NodeChange(action = "update")
    public User update(UserDTO data) {
        // oid,账号必须传入，其他修改根据传入的值做更新处理。
        Assert.notBlank(data.getOid(), "oid.input.null");
        Assert.notBlank(data.getAccount(), "account.input.null");
        //更新时候需要校验，不能存在相同的电话或者邮箱
        User user = userService.searchByOid(data.getOid());

        //原始密码， 新密码，确认密码都输入才修改密码。
        if (StringUtils.isNotBlank(data.getOldPassword()) && StringUtils.isNotBlank(data.getConfirmPassword()) && StringUtils.isNotBlank(data.getNewPassword())) {
            if (!StringUtils.equals(data.getNewPassword(), data.getConfirmPassword())) {
                throw new JWIException("newPassword.and.confirmPassword.different");
            }
            String oldPasswordMd5 = MD5Util.generateMD5(data.getOldPassword());
            if (!oldPasswordMd5.equals(user.getPassword())) {
                Assert.notNull(user, "user.oldPassword.error");
            }
            //设置新密码
            String newPasswordMd5 = MD5Util.generateMD5(data.getNewPassword());
            user.setPassword(newPasswordMd5);
        }
        user.setEmail(data.getEmail());
        user.setName(data.getName());
        user.setPhone(data.getPhone());
        user.setSignature(data.getSignature());
        user.setGender(data.getGender());
        if (StringUtil.isNotBlank(data.getAvatar())) {
            user.setAvatar(data.getAvatar());
        }
        if (data.getSecurityLevel() > 0) {
            user.setSecurityLevel(data.getSecurityLevel());
        }
        User updateUser = userService.update(user);

        Personnel personnel = personnelService.findByUserAccount(user.getAccount());
        // 更新账号时，同步更新人员对象
        if (!Objects.isNull(personnel)) {
            PersonnelUpdateDTO dto = new PersonnelUpdateDTO();
            BeanUtil.copyProperties(personnel, dto);
            dto.setName(data.getName());
            dto.setDisplayName(data.getName());
            dto.setPhone(data.getPhone());
            dto.setEmail(data.getEmail());
            personnelHelper.update(dto, false);
        }
        return updateUser;
    }

    @NodeChange(action = "update")
    @Override
    public User updateWhenPersonnelModify(String account, String email, String phone, String name) throws JWIException {
        User user = userService.searchByAccount(account);
        Assert.notNull(user, account + " user.not.found");
        user.setEmail(email);
        user.setPhone(phone);
        user.setName(name);
        return userService.update(user);
    }


    @Override
    public User disable(UserDTO userUpdateDTO) {
        User user = new User();
        BeanUtil.copyProperties(userUpdateDTO, user);
        return userService.disable(user);
    }


    @Override
    public User resetPassword(String userOid, String oldPassword, String confirmPassword, String newPassword) {
        Assert.notBlank(oldPassword, "oldPassword.null");
        Assert.notBlank(newPassword, "newPassword.null");
        Assert.notBlank(confirmPassword, "confirmPassword.null");
        if (!StringUtils.equals(newPassword, confirmPassword)) {
            throw new JWIException("newPassword.and.confirmPassword.different");
        }
        Assert.notNull(userOid, "userOid.null");
        User user = userService.searchByOid(userOid);
        Assert.notNull(user, "user.not.found");
        String oldPasswordMd5 = MD5Util.generateMD5(oldPassword);
        if (!oldPasswordMd5.equals(user.getPassword())) {
            Assert.notNull(user, "user.oldPassword.error");
        }
        //设置新密码
        String newPasswordMd5 = MD5Util.generateMD5(newPassword);
        user.setPassword(newPasswordMd5);
        user = userService.update(user);
        //缓存，设置密码更新时间
        redisTemplate.opsForHash().put(AuthConstants.JWI_PASSWORD_UPDATE_TIME, user.getAccount(), System.currentTimeMillis());
        return user;
    }


    @Override
    public UserDTO searchByOid(String userOid) {
        User user = userService.searchByOid(userOid);
        user.setPassword("");
        UserDTO dto = new UserDTO();
        BeanUtil.copyProperties(user, dto);
        return dto;
    }


    @Override
    public List<UserDTO> searchByOidList(List<String> oidList) {
        List<User> userList = userService.searchByOidList(oidList);
        return BeanUtil.cloneList(userList, UserDTO.class);
    }


    @Override
    public UserDTO initPassword(String userOid) throws JWIException {
        User user = userService.searchByOid(userOid);
        Assert.notNull(user, "user.not.found");
        PasswordPolicy passwordPolicy = passwordPolicyService.list().stream().findAny().get();
        String defaultPassword = passwordPolicy.getDefaultPassword();
        user.setPassword(MD5Util.generateMD5(defaultPassword));
        user = userService.update(user);
        UserDTO result = new UserDTO();
        BeanUtil.copyProperties(user, result);
        //缓存，设置密码更新时间
        redisTemplate.opsForHash().put(AuthConstants.JWI_PASSWORD_UPDATE_TIME, user.getAccount(), System.currentTimeMillis());
        return result;
    }

    @Override
    public boolean validateOldPassword(UserDTO dto) {
        Assert.notNull(dto, "user.null");
        Assert.notBlank(dto.getAccount(), "account.null");
        Assert.notBlank(dto.getOldPassword(), "oldPassword.null");
        String password = MD5Util.generateMD5(dto.getOldPassword());
        User user = userService.searchUserAccount(dto.getAccount(), password);
        if (null != user) {
            return true;
        }
        return false;
    }

    @Override
    public User passwordCycleUpdate(String account, String oldPassword, String confirmPassword, String newPassword) {
        Assert.notBlank(oldPassword, "oldPassword.null");
        Assert.notBlank(newPassword, "newPassword.null");
        Assert.notBlank(confirmPassword, "confirmPassword.null");
        if (!StringUtils.equals(newPassword, confirmPassword)) {
            throw new JWIException("newPassword.and.confirmPassword.different");
        }
        Assert.notBlank(newPassword, "account.null");
        User user = userService.searchByAccount(account);
        Assert.notNull(user, account + " user.not.found");
        String oldPasswordMd5 = MD5Util.generateMD5(oldPassword);
        if (!oldPasswordMd5.equals(user.getPassword())) {
            Assert.notNull(user, "user.oldPassword.error");
        }
        //设置新密码
        String newPasswordMd5 = MD5Util.generateMD5(newPassword);
        user.setPassword(newPasswordMd5);
        user = userService.update(user);
        //缓存，设置密码更新时间
        redisTemplate.opsForHash().put(AuthConstants.JWI_PASSWORD_UPDATE_TIME, user.getAccount(), System.currentTimeMillis());
        return user;
    }


    @Override
    public void refreshTokenRedis(UserTokenRefreshDTO dto) {
        //获取要修改密级的用户的token信息,如果redis拿不到则自己去后台查，可能token过期了。
        MultiTenantRedisUtil.changeDB(null);//切默认库
        //查询用户
        User user = userService.searchByAccount(dto.getAccount());
        //生成token并存租户库，如果自动切库正确的话
        String token = authHelper.generateJsonWebToken(user);
    }


    @Override
    public Map<String, File> findSignatureByAccount(List<String> accounts) {
        if (CollectionUtil.isEmpty(accounts)) {
            return new HashMap<>();
        }
        List<User> users = userService.searchByAccounts(accounts);

        return CollectionUtil.splitToMap(users, User::getAccount, User::getSignature);
    }
}
