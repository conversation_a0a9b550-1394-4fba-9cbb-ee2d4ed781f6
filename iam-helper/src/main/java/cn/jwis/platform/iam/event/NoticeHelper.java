package cn.jwis.platform.iam.event;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/27
 * @Description :消息通知工具
 */
@Async
@Component
@Slf4j
public class NoticeHelper {
    @Resource
    DataDistributionService dataDistributionService;

    public void sendMessage(List<? extends Object> baseEntityList, List<String> productKeyList, String type,
                            String action) {
        SessionHelper.addCurrentUser(new UserDTO());
        productKeyList.forEach(productKey -> {
            baseEntityList.stream().map(item -> JSONObject.parseObject(JSONObject.toJSON(item).toString())).forEach(item->{
                dataDistributionService.sendDataToMq(productKey, action, type, item);
            });
        });
    }

    public void sendMessage(List<TreeAbleEx> baseEntityList, String action, String productKey) {
        log.info("async sendMessage current thread {} ",Thread.currentThread().getId());
        SessionHelper.addCurrentUser(new UserDTO());
        baseEntityList.forEach(item -> {
            dataDistributionService.sendDataToMq(productKey, action, item.getType(),
                    JSONObject.parseObject(JSONObject.toJSON(item).toString()));
        });
    }


}
