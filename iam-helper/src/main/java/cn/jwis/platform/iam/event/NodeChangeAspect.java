package cn.jwis.platform.iam.event;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.product.helper.AccessCacheHelper;
import cn.jwis.platform.iam.user.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/17
 * @Description :定义事件处理
 */
@Aspect
@Component
@Slf4j
public class NodeChangeAspect {

    private static final Logger logger = LoggerFactory.getLogger(NodeChangeAspect.class);


    private static final String PRODUCT_KEY_SPACE = "space";


    @Resource
    NodeChangeHandle nodeChangeHandle;

    @Resource
    NoticeHelper noticeHelper;

    @Resource
    PersonnelHelper personnelHelper;

    @Resource
    AccessCacheHelper accessCacheHelper;


    @Pointcut("@annotation(cn.jwis.platform.iam.event.NodeChange)")
    public void NodeChangeAspect() {
    }


    @AfterReturning(value = "NodeChangeAspect()", returning = "returnValue")
    public void afterReturn(JoinPoint joinPoint, Object returnValue) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        NodeChange annotation = signature.getMethod().getAnnotation(NodeChange.class);
        List<BaseEntity> changeNodeList = nodeChangeHandle.handleEvent(joinPoint, returnValue);
        identifyNodeChangeAndNotice(changeNodeList, annotation.action());
    }

    public void identifyNodeChangeAndNotice(List<? extends BaseEntity> baseEntityList, String action) {
        Optional<? extends BaseEntity> optional = baseEntityList.stream().findAny();
        if (!optional.isPresent()) {
            return;
        }
        String type = optional.get().getType();

        // 账户信息更新 需要进行全员发送
        if (User.TYPE.equals(type)) {
            noticeHelper.sendMessage(baseEntityList, Lists.newArrayList(PRODUCT_KEY_SPACE), type, action);
        } else {
            Set<String> oidKeySet = baseEntityList.stream().map(BaseEntity::getOid).collect(Collectors.toSet());
            // 结构为 productKey oidSet
            Map<String, Set<String>> findResult = accessCacheHelper.findCache(oidKeySet, type);

            for (Map.Entry<String, Set<String>> item : findResult.entrySet()) {
                if(CollectionUtils.isEmpty(item.getValue())) {
                    continue;
                }
                if (Personnel.TYPE.equals(type)) {
                    accessCacheHelper.handPersonnelChange(item.getKey(), item.getValue(), action);
                } else {
                    noticeHelper.sendMessage(baseEntityList.stream().filter(entity -> item.getValue().contains(entity.getOid())).collect(Collectors.toList()), Lists.newArrayList(item.getKey()), type, action);
                }
            }
        }
    }

}
