package cn.jwis.platform.iam.personnel.dto;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class PersonnelOnPositionPageDTO extends PageSimpleDTO {

    @ApiModelProperty("岗位oid")
    @NotBlank(message = "岗位oid不能为空")
    private String positionOid;

    @ApiModelProperty("所属部门或公司oid")
    @NotBlank(message = "所属oid不能为空")
    private String orgId;
}
