package cn.jwis.platform.iam.account.dto;

import cn.jwis.platform.plm.foundation.numberrule.entity.valueObj.NumberRuleSegment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/17
 * @Description : 更新账号规则
 */
@Data
public class AccountRuleUpdateDTO {

    @ApiModelProperty(value = "显示名称",required = true)
    private String displayName ;

    @NotBlank(message = "oid不能为空")
    @ApiModelProperty(value = "oid",required = true)
    private String oid ;

    @Valid
    @ApiModelProperty(value = "编码规则",required = true)
    private List<NumberRuleSegment> numberRuleSegments;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "上下文type")
    private String containerType;

    @ApiModelProperty(value = "上下文oid")
    private String containerOid;

    @ApiModelProperty(value = "是否自动生成账户")
    private Boolean autoCreate;

    @NotNull(message = "账号规则类型不能为空")
    @ApiModelProperty(value = "账号规则类型")
    private Integer accountRuleType;
}
