package cn.jwis.platform.iam.account.hepler;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.iam.account.dto.AccountCreateDTO;
import cn.jwis.platform.iam.account.dto.AccountRuleCreateDTO;
import cn.jwis.platform.iam.account.dto.AccountRuleUpdateDTO;
import cn.jwis.platform.iam.accountrule.AccountRule;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.enums.AccountRuleType;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.personnel.PersonnelService;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.personnel.response.AccountWithPersonnelInfo;
import cn.jwis.platform.iam.personnel.response.EmptyUser;
import cn.jwis.platform.iam.relation.LinkTo;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.iam.user.dto.UserDTO;
import cn.jwis.platform.iam.user.helper.UserHelper;
import cn.jwis.platform.plm.foundation.common.response.DefaultNodeWithRels;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.numberrule.able.NumberAble;
import cn.jwis.platform.plm.foundation.numberrule.dto.NumberRuleCreateDTO;
import cn.jwis.platform.plm.foundation.numberrule.dto.NumberRuleUpdateDTO;
import cn.jwis.platform.plm.foundation.numberrule.entity.NumberRule;
import cn.jwis.platform.plm.foundation.numberrule.entity.valueObj.NumberRuleSegment;
import cn.jwis.platform.plm.foundation.numberrule.service.NumberRuleHelper;
import com.alibaba.excel.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class IamAccountHelperImpl implements IamAccountHelper {

    private static final String TYPE_SERIAL_NUMBER = "serialNumber";

    private static final String TYPE_CLS_CODE = "clsCode";

    private static final String TYPE_CONSTANT = "constant";

    private static final int NUMBER_RULE_LENGTH_MIN = 6;

    private static final Logger logger = LoggerFactory.getLogger(IamAccountHelperImpl.class);

    @Resource
    NumberRuleHelper numberRuleHelper;

    @Resource
    CommonService commonService;

    @Resource
    UserHelper userHelper;

    @Resource
    CommonAbilityService commonAbilityService;

    @Resource
    PersonnelService personnelService;

    @Resource
    PersonnelHelper personnelHelper;

    @Override
    public AccountRule createAccountRule(AccountRuleCreateDTO dto) {
        ValidationUtil.validate(dto);

        Assert.isTrue(AccountRuleType.TYPE_NUMBER.getType() == dto.getAccountRuleType() ||
                AccountRuleType.TYPE_USER_DEFINE.getType() == dto.getAccountRuleType(), "AccountRule Type error");

        AccountRule hisRule =
                commonService.dynamicQueryOne(AccountRule.TYPE, null, AccountRule.class);
        Assert.isTrue(ObjectUtils.isEmpty(hisRule), "AccountRule exists");

        AccountRule accountRule = new AccountRule();
        if (AccountRuleType.TYPE_NUMBER.getType() == dto.getAccountRuleType()) {
            BeanUtil.copyProperties(dto, accountRule);
        } else {
            NumberRuleCreateDTO numberRuleCreateDTO = new NumberRuleCreateDTO();
            checkAccountRule(dto.getNumberRuleSegments());
            BeanUtil.copyProperties(dto, numberRuleCreateDTO);
            NumberRule numberRule = numberRuleHelper.create(numberRuleCreateDTO);
            accountRule = new AccountRule();
            BeanUtil.copyProperties(dto, accountRule);
            accountRule.setNumberRuleOid(numberRule.getOid());
        }
        accountRule.setOid(OidGenerator.newOid());
        return commonService.create(accountRule);
    }

    @Override
    public AccountRule updateAccountRule(AccountRuleUpdateDTO dto) {
        ValidationUtil.validate(dto);
        AccountRule accountRule =
                commonService.findByOid(AccountRule.TYPE, dto.getOid(), AccountRule.class);
        Assert.isFalse(ObjectUtils.isEmpty(accountRule), "AccountRule not exists");
        // 只允许修改是否自动生成账号 以及自定义的规则细则
        if(AccountRuleType.TYPE_USER_DEFINE.getType() == accountRule.getAccountRuleType()) {
            NumberRuleUpdateDTO numberRuleUpdateDTO = new NumberRuleUpdateDTO();
            checkAccountRule(dto.getNumberRuleSegments());
            compareNumberLengthRule(dto.getNumberRuleSegments(),accountRule.getNumberRuleSegments());
            BeanUtil.copyProperties(dto, numberRuleUpdateDTO);
            numberRuleUpdateDTO.setOid(accountRule.getNumberRuleOid());
            numberRuleHelper.update(numberRuleUpdateDTO);
            accountRule.setNumberRuleSegments(dto.getNumberRuleSegments());
        }
        accountRule.setAutoCreate(dto.getAutoCreate());
        commonService.update(accountRule);
        return accountRule;
    }

    private void checkAccountRule(List<NumberRuleSegment> numberRuleSegmentList) {
        Assert.isTrue(numberRuleSegmentList.size() <= 2,String.format(Locale.ENGLISH, "Account rule mast has " +
                "Constant and  serialNumber"));
        Assert.isFalse(!numberRuleSegmentList.get(0).getType().equals(TYPE_CONSTANT), String.format(Locale.ENGLISH,
                "Account rule start mast %s ",TYPE_CONSTANT));
        Assert.isFalse(!numberRuleSegmentList.get(1).getType().equals(TYPE_SERIAL_NUMBER), String.format(Locale.ENGLISH,
                "Account rule second mast %s ",TYPE_SERIAL_NUMBER));

        for (NumberRuleSegment item : numberRuleSegmentList) {
            Assert.isFalse(TYPE_CLS_CODE.equals(item.getType()), String.format(Locale.ENGLISH, "Can not allow Type %s ",
                    TYPE_CLS_CODE));
        }
        int length = findNumberRuleLength(numberRuleSegmentList);
        Assert.isFalse(length < NUMBER_RULE_LENGTH_MIN, String.format(Locale.ENGLISH, "Number rule Min" +
                        " %s",
                NUMBER_RULE_LENGTH_MIN));
    }

    private void compareNumberLengthRule(List<NumberRuleSegment> updateRuleList, List<NumberRuleSegment> nowRuleList) {
        int updateLength = findNumberRuleLength(updateRuleList);
        int nowLength = findNumberRuleLength(nowRuleList);
        Assert.isFalse(nowLength > updateLength, String.format(Locale.ENGLISH, "New length can not " +
                        "smaller than " +
                        " %s",
                nowLength));
    }

    private int findNumberRuleLength(List<NumberRuleSegment> numberRuleSegmentList) {
        for (NumberRuleSegment item : numberRuleSegmentList) {
            Assert.isFalse(TYPE_CLS_CODE.equals(item.getType()), String.format(Locale.ENGLISH, "Can not allow Type %s ",
                    TYPE_CLS_CODE));
            if (TYPE_SERIAL_NUMBER.equals(item.getType())) {
                return item.getRule().getIntValue("length");
            }
        }
        return 0;
    }

    @Override
    public AccountRule queryAccountRule() {
        AccountRule accountRule =
                commonService.dynamicQueryOne(AccountRule.TYPE, null, AccountRule.class);
        if (ObjectUtils.isEmpty(accountRule)) {
            return null;
        }

        String ruleOid = accountRule.getOid();

        if (StringUtils.isNotBlank(accountRule.getNumberRuleOid())) {
            NumberRule numberRule = commonService.findByOid(NumberRule.TYPE, accountRule.getNumberRuleOid(),
                    NumberRule.class);
            BeanUtil.copyProperties(numberRule, accountRule);
        }
        accountRule.setOid(ruleOid);
        return accountRule;
    }

    @RestLock
    @Override
    public User generateAccount(AccountCreateDTO dto) {
        UserDTO userDto = new UserDTO();
        Assert.notBlank(dto.getAccount(), "account can not empty");
        BeanUtil.copyProperties(dto, userDto);
        User user;
        // 选择成员绑定账号
        if (StringUtils.isNotBlank(dto.getPersonnelOid())) {
            Personnel personnel = commonService.findByOid(Personnel.TYPE, dto.getPersonnelOid(), Personnel.class);
            Assert.isFalse(personnel.isInvalidFlag(),"Personnel is invalid");
            user = findByAccount(dto.getAccount());
            // 账号不存在则进行创建
            if (ObjectUtils.isEmpty(user)) {
                BeanUtil.copyProperties(personnel, userDto);
                userDto.setOid(StringUtils.EMPTY);
                user = userHelper.create(userDto);
            } else {
                FromToFilter filter = new FromToFilter();
                filter.setFromType(User.TYPE);
                filter.setToFilter(Condition.where("oid").eq(user.getOid()));
                filter.setType(LinkTo.TYPE);
                Assert.isFalse(commonService.countRelation(filter) > 0, "User hase be bind");
                userHelper.updateWhenPersonnelModify(dto.getAccount(), personnel.getEmail(), personnel.getPhone(),
                        personnel.getName());
            }
            personnelHelper.bindPersonnelWithUser(personnel, user);
        } else {
            // 直接创建账号场景
            user = userHelper.create(userDto);
        }
        return user;
    }

    @Override
    @RestLock
    public User generateAccountByPersonnel(Personnel personnel) {
        UserDTO userDto = new UserDTO();
        userDto.setAccount(personnel.getAssociatedAccount());
        userDto.setName(personnel.getName());
        userDto.setPhone(personnel.getPhone());
        userDto.setEmail(personnel.getEmail());
        return userHelper.create(userDto);
    }

    @Override
    public PageResult<AccountWithPersonnelInfo> fuzzyPage(PageSimpleDTO dto) {
        return personnelService.fuzzyPageAccount(buildSearchCondition(dto.getSearchKey()), buildOrder(), dto.getIndex(),
                dto.getSize());
    }

    @Override
    public String getNextAccount() {
        return generateAccount();
    }

    @Override
    public List<EmptyUser> queryEmptyUser() {
        List<EmptyUser> userList = personnelService.queryEmptyUser();
        EmptyUser user = new EmptyUser();
        user.setAccount(getNextAccount());
        userList.add(0, user);
        return userList;
    }

    @Override
    public List<User> queryByAccount(List<String> accountList) {
        return commonService.dynamicQuery(User.TYPE,Condition.where("account").in(accountList),User.class);
    }

    private String generateAccount() {
        AccountRule accountRule = queryAccountRule();
        Assert.notNull(accountRule, "can not find accountRule");
        Assert.notBlank(accountRule.getNumberRuleOid(), "AccountRule not set");
        NumberAble numberAble = new DefaultNodeWithRels();
        numberAble.setModelDefinition("AccountCreate");
        numberAble.setType("AccountCreate");
        commonAbilityService.initNumber(numberAble, accountRule);
        return numberAble.getNumber();
    }

    public Condition buildSearchCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("phone").contain(searchKey))
                    .or(Condition.where("account").contain(searchKey));
        }
        return condition;
    }


    private User findByAccount(String account) {
        return commonService.dynamicQueryOne(User.TYPE,
                Condition.where("account").eq(account),
                User.class);
    }

    private Order buildOrder() {
        return new Order().desc("name");
    }

}
