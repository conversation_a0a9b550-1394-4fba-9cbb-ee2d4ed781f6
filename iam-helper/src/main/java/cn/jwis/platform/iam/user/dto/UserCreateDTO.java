package cn.jwis.platform.iam.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Created with IntelliJ IDEA.
 *
 * @CreatedBy: YC 2019/4/19 14:22
 * @Description:
 */
@Data
public class UserCreateDTO {

    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    private String account;

    private String password;

    @ApiModelProperty("邮箱")
    //@Email(message = "联系人邮箱格式错误")
    //@NotBlank(message = "邮箱不能为空")
    private String email;

    @ApiModelProperty("手机")
    private String phone;
/*
    @ApiModelProperty("手机号")
    private String mobile;*/

    @ApiModelProperty("性别 true 男  false 女")
    private boolean gender;

    @ApiModelProperty("租户oid")
    private String tenantOid;

    private String avatar;

    @ApiModelProperty("签名")
    private String signature;

    @ApiModelProperty("部门oid")
    private String orgOid;

    @ApiModelProperty("是否为部门管理者")
    private boolean admin;

    @ApiModelProperty("密级")
    private String secretId;

    private String createBy;
    private long createDate;
    private String updateBy;
    private long updateDate;
    private boolean disable;

}