package cn.jwis.platform.iam.product.scheduler;

import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.product.entity.ExpireRecord;
import cn.jwis.platform.iam.product.entity.Product;
import cn.jwis.platform.iam.product.entity.ProductLicense;
import cn.jwis.platform.iam.product.helper.ProductHelper;
import cn.jwis.platform.iam.relation.AccessTo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/14
 * @Description :授权超期计算定时任务
 */
@Component
public class ProductAccessComputeScheduler {

    private static final Logger logger = LoggerFactory.getLogger(ProductAccessComputeScheduler.class);

    private static final String SCHEDULER_KEY = "SCHEDULER_KEY";

    @Resource
    CommonService commonService;

    @Resource
    ProductHelper productHelper;

    @Resource
    RedisTemplate redisTemplate;
    /**
     * 每天定时1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    private void compute() {
        boolean result = redisTemplate.opsForValue().setIfAbsent(SCHEDULER_KEY,System.currentTimeMillis(),3, TimeUnit.SECONDS);
        if(!result) {
            logger.info("Compute is running cancel");
        }
        logger.info("start compute Expire");
        List<Product> productList = commonService.dynamicQuery(Product.TYPE, null, Product.class);
        productList.forEach(item -> {
            logger.info("start compute productKey:{}", item.getProductKey());
            ProductLicense productLicense = productHelper.getProductLicense(item.getOid());
            long delNum;
            if(productHelper.isExpire(productLicense.getExpirationDate())) {
                logger.info("License is  Expire productKey:{}", item.getProductKey());
                delNum = deleteAccessToLink(item.getOid(), item.getProductKey(),
                        false, productLicense.getExpirationDate());
            } else {
                delNum = deleteAccessToLink(item.getOid(), item.getProductKey(),
                        true, productLicense.getExpirationDate());
            }
            logger.info("delete access link num:{},productKey:{}", delNum, item.getProductKey());
        });
        redisTemplate.delete(SCHEDULER_KEY);
    }

    private long deleteAccessToLink(String productOid,String productKey,boolean needCheckExpire,long licenseExpireDate) {
        List<AccessTo> accessToList = productHelper.queryAccessToList(productOid);
        logger.info("deleteAccessToLink  all accessTo size:{}, productKey:{}", accessToList.size(), productKey);
        if(needCheckExpire) {
            logger.info("needCheckExpire  Expire productKey:{}", productKey);
            accessToList = accessToList.stream().filter(item->ObjectUtils.isEmpty(item.getExpirationDate()) ? false:
                    productHelper.isExpire(item.getExpirationDate())).collect(Collectors.toList());
            logger.info("After check expire  accessTo size:{}, productKey:{}", accessToList.size(), productKey);
        }
        if (CollectionUtils.isEmpty(accessToList)) {
            return 0L;
        }
        List<String> deleteList = accessToList.stream().map(AccessTo::getOid).collect(Collectors.toList());
        List<ExpireRecord> expireRecordList =
                createExpireRecords(productOid, productKey,
                        accessToList.stream().filter(item -> Personnel.TYPE.equals(item.getFromType())).collect(Collectors.toList()), licenseExpireDate);

        FromToFilter filter = new FromToFilter();
        filter.setType(AccessTo.TYPE);
        filter.setRelFilter(Condition.where("oid").in(deleteList));
        logger.info("create Record size:{}, productKey:{}", expireRecordList.size(), productKey);
        expireRecordList.forEach(item -> commonService.create(item));
        return commonService.deleteRelation(filter);
    }

    private List<ExpireRecord> createExpireRecords(String productOid, String productKey,
                                                   List<AccessTo> list,long licenseExpireDate) {
        List<ExpireRecord> expireRecordList =
                list.stream().map(item -> {
                    ExpireRecord expireRecord = new ExpireRecord();
                    expireRecord.setActiveDate(item.getActiveDate());
                    expireRecord.setExpirationDate(ObjectUtils.isEmpty(item.getExpirationDate()) ? licenseExpireDate
                            : item.getExpirationDate());
                    expireRecord.setOid(OidGenerator.newOid());
                    expireRecord.setProductKey(productKey);
                    expireRecord.setProductOid(productOid);
                    expireRecord.setPersonnelOid(item.getFromOid());
                    return expireRecord;
                }).collect(Collectors.toList());
        expireRecordList.forEach(record -> {
            commonService.create(record);
        });
        return expireRecordList;
    }

}
