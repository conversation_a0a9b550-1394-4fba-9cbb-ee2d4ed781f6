package cn.jwis.platform.iam.auth.dto;

import cn.jwis.platform.iam.user.User;
import lombok.Data;

@Data
public class AuthDTO {

    private String tenantAlias;

    private String tenantOid;

    private String accesstoken;

    private int code;

    private String tips;

    private User user;


    public void setCodeAndMsg(int code, String msg) {
        this.code = code;
        this.tips = msg;
    }
}
