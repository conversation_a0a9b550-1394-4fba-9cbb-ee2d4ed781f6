package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.i18n.I18nUtils;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.common.help.MakeTreeHelper;
import cn.jwis.platform.iam.enums.TreeBuildType;
import cn.jwis.platform.iam.event.NodeChange;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.organization.CompanyService;
import cn.jwis.platform.iam.organization.DepartmentService;
import cn.jwis.platform.iam.organization.PositionService;
import cn.jwis.platform.iam.organization.dto.ADDomainRefreshDTO;
import cn.jwis.platform.iam.organization.dto.CompanyCreateDTO;
import cn.jwis.platform.iam.organization.dto.CompanyUpdateDTO;
import cn.jwis.platform.iam.organization.dto.SynchronizeOrgVarDTO;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.personnel.dto.PersonnelAddAnyDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelCreateDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelUpdateDTO;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.personnel.response.PersonnelWithUser;
import cn.jwis.platform.iam.relation.BelongTo;
import cn.jwis.platform.iam.relation.InValidTo;
import cn.jwis.platform.iam.response.*;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.structure.TreeNode;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 */
@Service
@Transactional
public class CompanyHelperImpl implements CompanyHelper {

    protected static final Class<Company> POJO_CLASS = Company.class;

    private static final Logger logger = LoggerFactory.getLogger(CompanyHelperImpl.class);

    @Value("#{'${personnel.compare.keys:number,name,displayName,phone,email,description}'.split(',')}")
    private List<String> personnelComKeys;

    @Value("#{'${iam.retain.account:hll,lijiantao}'.split(',')}")
    private List<String> iamRetainAccount;

    @Resource
    CompanyService companyService;

    @Resource
    CommonService commonService;

    @Resource
    MakeTreeHelper makeTreeHelper;

    @Resource
    PersonnelHelper personnelHelper;

    @Resource
    DepartmentService departmentService;

    @Autowired
    PositionService positionService;

    @Resource
    CommonAbilityHelper commonAbilityHelper;

    @Resource
    BeAspectHelper beAspectHelper;

    @Autowired
    ADDomainCall adDomainCall;

    @Override
    @NodeChange(action = "create")
    @RestLock
    public Company create(CompanyCreateDTO dto) {
        ValidationUtil.validate(dto);
        String name = dto.getName();
        Assert.isNull(findByName(name), "common.name.repeat");
        String parentCompanyOid = dto.getParentCompanyOid();
        Company parentCompany = findByOid(parentCompanyOid);
        Assert.notNull(parentCompany, "company.parent.not.exits");
        Company company = BeanUtil.copyProperties(dto, new Company());
        company.setLevel(parentCompany.getLevel() + 1);
        company.setParentCompanyOid(parentCompanyOid);
        commonAbilityHelper.doCreate(company);
        //创建与父的关系  (self)-[:BELONG_TO]->(parent)
        commonService.createRelation(new BelongTo(Company.TYPE, company.getOid(), Company.TYPE, parentCompanyOid));
        return company;
    }

    @Override
    @NodeChange(action = "create")
    @RestLock
    public synchronized Company createRoot(CompanyCreateDTO dto) {
        ValidationUtil.validate(dto);
        String name = dto.getName();
        Assert.isNull(findByName(name), "common.name.repeat");
        Company root = getRootCompany();
        if (root != null) {
            return root;
        }
        // 清空父级公司保证数据正确
        dto.setParentCompanyOid(StringUtils.EMPTY);
        Company company = BeanUtil.copyProperties(dto, new Company());
        company.setLevel(1);
        return commonAbilityHelper.doCreate(company);
    }

    private Company getRootCompany() {
        return commonService.dynamicQueryOne(Company.TYPE, Condition.where("parentCompanyOid").eq(StringUtil.EMPTY),
                Company.class);
    }

    @Override
    @NodeChange(action = "update")
    public Company update(CompanyUpdateDTO dto) {
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        Company byOid = findByOid(oid);
        Assert.notNull(byOid, "company.not.exits");
        // 若更新DisplayName则同步更新name
        if(!dto.getDisplayName().equalsIgnoreCase(byOid.getDisplayName())) {
            dto.setName(dto.getDisplayName());
        }
        Company byCode = findByName(dto.getName());
        Assert.isTrue(byCode == null || byCode.getOid().equals(oid), "common.name.repeat");
        BeanUtil.copyProperties(dto, byOid);
        return commonAbilityHelper.doUpdate(byOid);
    }

    @Override
    @NodeChange(action = "delete")
    public Long delete(String oid, String type) {
        if (StringUtil.isBlank(oid)) {
            return 0L;
        }
        Long aLong = commonService.countInterRelation(type, oid, null);
        Assert.isTrue(aLong == 0, "company.has.bind");
        return commonService.delete(type, oid);
    }

    @Override
    public List<Company> fuzzy(String searchKey) {
        Condition condition = buildSearchCondition(searchKey);
        return commonService.dynamicQuery(Company.TYPE, condition, buildOrder(), POJO_CLASS);
    }

    @Override
    public PageResult<Company> fuzzyPage(PageSimpleDTO dto) {
        Condition condition = buildSearchCondition(dto.getSearchKey());
        return commonService.dynamicQueryPage(Company.TYPE, condition, buildOrder(), dto.getIndex(), dto.getSize(),
                POJO_CLASS);
    }

    @Override
    public TreeNode findOrgTree(Integer buildType) {
        if (TreeBuildType.TYPE_ONLY_ORG.getType() == buildType) {
            return makeTreeHelper.makeOrgTree(companyService.getCurrentIdOrgMap(true).values());
        } else {
            List<TreeAbleEx> treeAbleExList;
            if (TreeBuildType.TYPE_NOT_EMPTY_POSITION_HAS_ACCOUNT.getType() == buildType) {
                treeAbleExList = filterNotAccountPersonnelNode(companyService.findOrgTree());
                treeAbleExList = findLinkParentWithNodeList(treeAbleExList);
            } else {
                treeAbleExList = companyService.findOrgTree();
            }
            return makeTreeHelper.makeOrgTree(rebuildPositionWithPersonnel(treeAbleExList, buildType));
        }

    }

    @Override
    public TreeNode fuzzyOrgTree(String searchKey, Integer buildType) {
        logger.info("fuzzyOrgTree search key {} , buildType {}", searchKey, buildType);
        if (ObjectUtils.isEmpty(searchKey)) {
            return findOrgTree(buildType);
        }
        // 搜索结果为匹配到的岗位 部门 公司 或人员
        List<TreeAbleEx> searchList = companyService.fuzzyOrgTree(buildTreeSearchCondition(searchKey));
        logger.info("fuzzyOrgTree getSearch result size {} ", searchList.size());
        if (CollectionUtils.isEmpty(searchList)) {
            return null;
        }
        // 过滤 无账号人员 并寻找剩余节点的公共父级，组装为完整树结构
        if (TreeBuildType.TYPE_NOT_EMPTY_POSITION_HAS_ACCOUNT.getType() == buildType) {
            searchList = filterNotAccountPersonnelNode(searchList);
            logger.info("filterNotAccountPersonnelNode result size {} ", searchList.size());
        // 非空岗位以及所有显示类型 下对搜索结果中的岗位进行人员查找
        } else if (TreeBuildType.TYPE_NOT_EMPTY_POSITION.getType() == buildType || TreeBuildType.TYPE_ALL_POSITION.getType() == buildType) {
            findPositionBindPersonnel(searchList);
            logger.info("findPositionBindPersonnel result size {} ", searchList.size());
        }
        searchList = findLinkParentWithNodeList(searchList);
        logger.info("findLinkParentWithNodeList result size {} ", searchList.size());
        return makeTreeHelper.makeOrgTree(rebuildPositionWithPersonnel(searchList, buildType));
    }

    private void findPositionBindPersonnel(List<TreeAbleEx> searchList) {
        Set<String> positionOidSet =
                searchList.stream().filter(item -> Position.TYPE.equals(item.getType())).map(TreeAbleEx::getCurrentNodeId).
                        collect(Collectors.toSet());
        Map<String, TreeAbleEx> listMap = this.getParentIdPerMap();
        positionOidSet.forEach(item -> {
            if (!ObjectUtils.isEmpty(listMap.get(item))) {
                searchList.add(listMap.get(item));
            }
        });
    }

    @Override
    public List<TreeAbleEx> findLinkParentWithNodeList(List<TreeAbleEx> searchList) {
        Set<String> parentIdSet = searchList.stream().map(TreeAbleEx::getParentNodeId).collect(Collectors.toSet());
        Set<String> currentIdSet = searchList.stream().map(TreeAbleEx::getCurrentNodeId).collect(Collectors.toSet());
        parentIdSet.removeAll(currentIdSet);
        List<TreeAbleEx> result = new ArrayList<>();
        findNextParentLink(parentIdSet,result,getCurrentIdOrgMap());
        searchList.addAll(result);
        return searchList;
    }

    private void findNextParentLink(Set<String> findCurrentOidSet, List<TreeAbleEx> resultList, Map<String, TreeAbleEx> currentOidOrgMap) {
        if (CollectionUtils.isEmpty(findCurrentOidSet)) {
            return;
        }
        Set<String> nextLevelOidSet = new HashSet<>();
        for (String currentOid : findCurrentOidSet) {
            TreeAbleEx currentNode = currentOidOrgMap.get(currentOid);
            if (ObjectUtils.isEmpty(currentNode)) {
                logger.info("can not find node by {}", currentOid);
                continue;
            }
            resultList.add(currentNode);
            if (StringUtils.isBlank(currentNode.getParentNodeId())) {
                continue;
            }
            nextLevelOidSet.add(currentNode.getParentNodeId());
        }
        findNextParentLink(nextLevelOidSet, resultList, currentOidOrgMap);
    }

    private List<TreeAbleEx> filterNotAccountPersonnelNode(List<TreeAbleEx> treeAbleExList) {
        return treeAbleExList.stream().filter(item -> {
            if (Personnel.TYPE.equals(item.getType())) {
                Personnel personnel = (Personnel) item;
                return StringUtils.isNotBlank(personnel.getAssociatedAccount());
            }
            return false;
        }).collect(Collectors.toList());
    }

    @Override
    public TreeNode findOrgTreeWithAccessFlag(Set<String> personnelOidSet, String searchKey) {
        List<TreeAbleEx> searchList;
        if (ObjectUtils.isEmpty(searchKey)) {
            searchList = companyService.getCurrentIdPerMap().values().stream().collect(Collectors.toList());
        } else {
            // 搜索结果为匹配到的岗位 部门 公司 或人员
            searchList = companyService.fuzzyOrgTree(buildTreeSearchCondition(searchKey));
        }
        if (CollectionUtils.isEmpty(searchList)) {
            return null;
        }
        searchList = filterNotAccountPersonnelNode(searchList);
        // 过滤已授权的人员
        searchList =
                searchList.stream().filter(item -> Personnel.TYPE.equals(item.getType()) && !personnelOidSet.contains(item.getCurrentNodeId())).collect(Collectors.toList());
        searchList = findLinkParentWithNodeList(searchList);
        return makeTreeHelper.makeOrgTree(rebuildPositionWithPersonnel(searchList,
                TreeBuildType.TYPE_NOT_EMPTY_POSITION.getType()));
    }

    @Override
    public Map<String, TreeAbleEx> getCurrentIdOrgMap() {
        return companyService.getCurrentIdOrgMap(true);
    }

    @Override
    public Map<String, List<TreeAbleEx>> getCurrentIdPerMap() {
        return companyService.getCurrentIdPerMap().values().stream().collect(Collectors.toMap(node -> node.getCurrentNodeId(),
                node -> Lists.newArrayList(node),
                (List<TreeAbleEx> newValueList, List<TreeAbleEx> oldValueList) -> {
                    oldValueList.addAll(newValueList);
                    return oldValueList;
                }));
    }

    private Map<String, TreeAbleEx> getParentIdPerMap() {
        return companyService.getCurrentIdPerMap().values().stream().collect(Collectors.toMap(k -> k.getParentNodeId(),
                t -> t));
    }

    @Override
    public Map<String, List<TreeAbleEx>> getParentIdOrgMap() {
        return companyService.getParentIdOrgMap();
    }

    private Order buildOrder() {
        return new Order().desc("name");
    }

    @Override
    public Company findByOid(String oid) {
        return commonService.findByOid(Company.TYPE, oid, POJO_CLASS);
    }

    @Override
    public Company findByName(String name) {
        return commonService.dynamicQueryOne(Company.TYPE,
                Condition.where("name").eq(name),
                POJO_CLASS);
    }

    @Override
    public LevelNodeInfo queryByLevel(String oid) {
        String parentOid;
        if (StringUtils.isBlank(oid)) {
            parentOid = getRootCompany().getOid();
        } else {
            parentOid = oid;
        }
        TreeAbleEx org = commonService.findByOid(Department.TYPE, parentOid, Department.class);
        LevelNodeInfo result;
        if (ObjectUtils.isEmpty(org)) {
            org = commonService.findByOid(Company.TYPE, parentOid, Company.class);
            Assert.notNull(org, "common.org.not.find");
            result = BeanUtil.copyProperties(org, new LevelNodeCompany());
        } else {
            result = BeanUtil.copyProperties(org, new LevelNodeInfo());
        }
        String parentDisplayName = org.getDisplayName();
        Map<String, List<TreeAbleEx>> parentIdOrgMap = companyService.getParentIdOrgMap();
        // 人员归属岗位信息
        Map<String, PersonnelWithUser> belongPersonnelMap =
                getPersonnelWithAvatar().entrySet().stream().filter(per -> StringUtils.isNotBlank(per.getValue().getAssociatedAccount())).collect(Collectors.toMap(item -> item.getKey(),
                        item -> item.getValue()));
        List<TreeAbleEx> childOrgList = parentIdOrgMap.get(parentOid);
        Assert.notEmpty(childOrgList, "common.org.not.find");
        // 需要满足 非岗位信息 或 岗位是人员所属岗位 过滤无账号人员
        result.setChild(childOrgList.stream().filter(item -> !Position.TYPE.equals(item.getType()) || belongPersonnelMap.containsKey(item.getCurrentNodeId())).
                map(item -> buildLevelNode(item, parentIdOrgMap, belongPersonnelMap, parentDisplayName)).
                filter(item -> Personnel.TYPE.equals(item.getType()) || item.getPersonnelNum() > 0).collect(Collectors.toList()));
        //查找并设置人员备注信息
        personnelHelper.findAndSetUserDescription(result.getChild().stream().filter(item -> item instanceof LevelNodePersonnel).map(item -> (LevelNodePersonnel) item).collect(Collectors.toMap(key -> key.getUserOid(), value -> value)));
        // 人员权重最高 其他按照 类型-名称排序
        Collections.sort(result.getChild(), (o1, o2) -> {
            if (!StringUtils.equals(o1.getType(), o2.getType())) {
                if (Personnel.TYPE.equals(o1.getType())) {
                    return -1;
                } else if (Personnel.TYPE.equals(o2.getType())) {
                    return 1;
                } else {
                    return StringUtils.compare(o2.getType(), o1.getType());
                }
            }
            return StringUtils.compare(o1.getDisplayName(), o2.getDisplayName());
        });
        return result;
    }

    private LevelNodeInfo buildLevelNode(TreeAbleEx treeAbleEx, Map<String, List<TreeAbleEx>> parentIdOrgMap,
                                         Map<String, PersonnelWithUser> belongPersonnelMap, String departmentName) {
        if (Position.TYPE.equals(treeAbleEx.getType())) {
            PersonnelWithUser personnel = belongPersonnelMap.get(treeAbleEx.getCurrentNodeId());
            belongPersonnelMap.remove(treeAbleEx.getCurrentNodeId());
            LevelNodePersonnel levelNodePersonnel = BeanUtil.copyProperties(personnel, new LevelNodePersonnel());
            levelNodePersonnel.setContactsOid(personnel.getOid());
            levelNodePersonnel.setDepartmentName(departmentName);
            levelNodePersonnel.setPositionName(treeAbleEx.getDisplayName());
            levelNodePersonnel.setAccount(personnel.getAssociatedAccount());
            levelNodePersonnel.setDisplayName(personnel.getDisplayName());
            return levelNodePersonnel;
        } else if (Company.TYPE.equals(treeAbleEx.getType())) {
            LevelNodeCompany levelNodeCompany = BeanUtil.copyProperties(treeAbleEx, new LevelNodeCompany());
            levelNodeCompany.setContactsOid(treeAbleEx.getOid());
            levelNodeCompany.setPersonnelNum(findBelongPeopleNum(levelNodeCompany.getContactsOid(), parentIdOrgMap,
                    belongPersonnelMap.keySet()));
            return levelNodeCompany;
        } else {
            LevelNodeInfo department = new LevelNodeInfo();
            BeanUtil.copyProperties(treeAbleEx, department);
            department.setContactsOid(treeAbleEx.getOid());
            department.setPersonnelNum(findBelongPeopleNum(department.getContactsOid(), parentIdOrgMap,
                    belongPersonnelMap.keySet()));
            return department;
        }
    }

    private int findBelongPeopleNum(String departmentOid, Map<String, List<TreeAbleEx>> parentIdOrgMap,
                                    Set<String> hasPersonnelPositionOidSet) {
        Set<String> childPositionOidSet = new HashSet<>();
        findAllPosition(departmentOid, parentIdOrgMap, childPositionOidSet);
        childPositionOidSet.retainAll(hasPersonnelPositionOidSet);
        return childPositionOidSet.size();
    }

    private void findAllPosition(String departmentOid, Map<String, List<TreeAbleEx>> parentIdOrgMap,
                                 Set<String> positionOidSet) {
        List<TreeAbleEx> treeAbleExList = parentIdOrgMap.get(departmentOid);
        if (CollectionUtils.isEmpty(treeAbleExList)) {
            return;
        }
        treeAbleExList.forEach(item -> {
            if (Position.TYPE.equals(item.getType())) {
                positionOidSet.add(item.getCurrentNodeId());
            } else if (Department.TYPE.equals(item.getType())) {
                findAllPosition(item.getCurrentNodeId(), parentIdOrgMap, positionOidSet);
            }
        });
    }

    private Map<String, PersonnelWithUser> getPersonnelWithAvatar() {
        return companyService.getCurrentIdPerMap().values().stream()
                .collect(Collectors.toMap(node -> node.getParentNodeId(), t -> BeanUtil.copyProperties(t,
                        new PersonnelWithUser())));
    }

    private Condition buildSearchCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("number").contain(searchKey))
                    .or(Condition.where("displayName").contain(searchKey));
        }
        return condition;
    }

    private Condition buildTreeSearchCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("number").contain(searchKey))
                    .or(Condition.where("associatedAccount").contain(searchKey))
                    .or(Condition.where("displayName").contain(searchKey));
        }
        return condition;
    }

    /**
     * 根据岗位是否分配人员信息 进行数据重新组装 1 只组装有人员岗位 2 只组装无人员岗位 3 全部 4 只需要部门
     *
     * @param treeAbleExList 查询结果集
     * @param buildType      查询类型
     * @return 重新筛选后数据
     */
    private List<TreeAbleEx> rebuildPositionWithPersonnel(List<TreeAbleEx> treeAbleExList, Integer buildType) {
        List<TreeAbleEx> resultList = new ArrayList<>(treeAbleExList);
        Map<String, TreeAbleEx> positionMap = new HashMap<>();
        Map<String, List<TreeAbleEx>> personnelMap = new HashMap<>();
        // 将数据分为人员 岗位
        for (Iterator<TreeAbleEx> iterator = resultList.iterator(); iterator.hasNext(); ) {
            TreeAbleEx treeAbleEx = iterator.next();
            if (treeAbleEx.getType().equalsIgnoreCase(Personnel.TYPE)) {
                personnelMap.computeIfAbsent(treeAbleEx.getParentNodeId(), key -> Lists.newArrayList()).add(treeAbleEx);
                iterator.remove();
            } else if (treeAbleEx.getType().equalsIgnoreCase(Position.TYPE)) {
                positionMap.put(treeAbleEx.getCurrentNodeId(), treeAbleEx);
                iterator.remove();
            }
        }
        resultList.addAll(groupByBuildType(positionMap, personnelMap, buildType));
        return resultList;
    }

    // 将人员岗位
    private List<TreeAbleEx> groupByBuildType(Map<String, TreeAbleEx> positionMap,
                                              Map<String, List<TreeAbleEx>> personnelMap, int buildType) {
        List<TreeAbleEx> resultList = new ArrayList<>();
        // 根据所需的数据类型 进行组装
        for (Map.Entry<String, List<TreeAbleEx>> entry : personnelMap.entrySet()) {
            entry.getValue().forEach(personnel -> {
                TreeAbleEx position = positionMap.get(entry.getKey());
                positionMap.remove(entry.getKey());
                if (ObjectUtils.isEmpty(position)) {
                    logger.info("cant not find Belong Position by key {}", entry.getKey());
                } else {
                    if (TreeBuildType.TYPE_NOT_EMPTY_POSITION.getType() == buildType || TreeBuildType.TYPE_ALL_POSITION.getType() == buildType || TreeBuildType.TYPE_NOT_EMPTY_POSITION_HAS_ACCOUNT.getType() == buildType) {
                        PersonnelWithPosition personnelWithPosition = new PersonnelWithPosition();
                        BeanUtil.copyProperties(personnel, personnelWithPosition);
                        personnelWithPosition.setPersonnelOid(personnel.getOid());
                        // 下游业务系统 需要使用userOid替换personnelOid
                        if(TreeBuildType.TYPE_NOT_EMPTY_POSITION_HAS_ACCOUNT.getType() == buildType) {
                            personnelWithPosition.setOid(personnelWithPosition.getUserOid());
                        }
                        setPosition(personnelWithPosition, (Position) position);
                        resultList.add(personnelWithPosition);
                    }
                }
            });
        }

        if (TreeBuildType.TYPE_EMPTY_POSITION.getType() == buildType || TreeBuildType.TYPE_ALL_POSITION.getType() == buildType) {
            for (Map.Entry<String, TreeAbleEx> entry : positionMap.entrySet()) {
                PersonnelWithPosition personnelWithPosition = new PersonnelWithPosition();
                setPosition(personnelWithPosition, (Position) entry.getValue());
                resultList.add(personnelWithPosition);
            }
        }
        return resultList;
    }

    private void setPosition(PersonnelWithPosition personnelWithPosition, Position position) {
        personnelWithPosition.setPositionDisplayName(position.getDisplayName());
        personnelWithPosition.setParentType(position.getParentType());
        personnelWithPosition.setParentOid(position.getParentOid());
        personnelWithPosition.setPositionDefOid(position.getPositionDefOid());
        personnelWithPosition.setPositionOid(position.getOid());
        personnelWithPosition.setReportPositionOid(position.getReportPositionOid());
    }


    @Override
    public void exportExcel(String fromType, String fromOid, HttpServletResponse response) throws IOException {
        OutputStream os = new BufferedOutputStream(response.getOutputStream());
        Assert.isTrue(Company.TYPE.equals(fromType) || Department.TYPE.equals(fromType),"Export type error");
        TreeAbleEx treeAbleEx;
        String companyName;
        String departmentName;
        if(Company.TYPE.equals(fromType)) {
            treeAbleEx = commonService.findByOid(Company.TYPE,fromOid,Company.class);
            companyName = treeAbleEx.getDisplayName();
            departmentName = StringUtils.EMPTY;
        } else {
            treeAbleEx = commonService.findByOid(Department.TYPE,fromOid,Department.class);
            departmentName = treeAbleEx.getDisplayName();
            companyName = findCompanyName(fromType, fromOid, companyService.getCurrentIdOrgMap(true));
        }

        Assert.notNull(treeAbleEx, String.format(Locale.ENGLISH, "Type %s , oid %s  does not exists", fromType,
                fromOid));
        response.setContentType("application/vnd.ms-excel");
        response.addHeader("Content-Disposition", String.format(Locale.CHINESE, "attachment;filename=%s.xlsx",
                URLEncoder.encode(String.format(Locale.CHINESE, "JWI_EXPORT_%s_%s", fromType, treeAbleEx.getDisplayName(),
                        "utf-8"))));
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        List<OrgExcelRow> rows = findPersonnelAndBuildOrgRow(fromOid, companyName, departmentName);
        EasyExcel.write(os, OrgExcelRow.class)
                .sheet(treeAbleEx.getDisplayName())
                .doWrite(rows);
    }

    private List<OrgExcelRow> findPersonnelAndBuildOrgRow(String fromOid,String companyName,String departmentName) {
        Map<String, TreeAbleEx> parentPersonnelMap =
                companyService.getCurrentIdPerMap().values().stream().collect(Collectors.toMap(node -> node.getParentNodeId(), t -> t));
        List<OrgExcelRow> orgExcelRowList = new ArrayList<>();
        buildOrgRow(companyService.getParentIdOrgMap(), companyName, departmentName, fromOid, orgExcelRowList,
                parentPersonnelMap);
        Collections.sort(orgExcelRowList, (o1, o2) -> {
            if (o1.getCompanyName().equals(o2.getCompanyName())) {
                if (o1.getDepartment().equals(o2.getDepartment())) {
                    return o1.getPosition().compareTo(o2.getPosition());
                } else {
                    return o1.getDepartment().compareTo(o2.getDepartment());
                }
            } else {
                return o2.getCompanyName().compareTo(o1.getCompanyName());
            }
        });
        return orgExcelRowList;
    }

    /**
     * 根据当前组织类型向上查找最近的 公司
     *
     * @param fromType
     * @param fromOid
     * @param orgMap
     * @return
     */
    private String findCompanyName(String fromType, String fromOid, Map<String, TreeAbleEx> orgMap) {
        if (Company.TYPE.equals(fromType)) {
            Company company = commonService.findByOid(Company.TYPE, fromOid, Company.class);
            Assert.notNull(company, "Can not find company");
            return company.getName();
        } else {
            TreeAbleEx treeAbleEx = orgMap.get(fromOid);
            TreeAbleEx parent = orgMap.get(treeAbleEx.getParentNodeId());
            Assert.notNull(parent, "Can not find company and find parent error");
            return findCompanyName(parent.getType(), parent.getCurrentNodeId(), orgMap);
        }
    }

    private void buildOrgRow(Map<String, List<TreeAbleEx>> orgMap, String companyName, String departmentStr,
                             String orgOid, List<OrgExcelRow> orgExcelRowList,
                             Map<String, TreeAbleEx> parentPersonnelMap) {
        List<TreeAbleEx> orgList = orgMap.get(orgOid);
        if (CollectionUtils.isEmpty(orgList)) {
            return;
        }
        orgList.forEach(item -> {
            if (Position.TYPE.equals(item.getType())) {
                OrgExcelRow orgExcelRow = new OrgExcelRow();
                orgExcelRow.setCompanyName(companyName);
                orgExcelRow.setPosition(item.getDisplayName());
                orgExcelRow.setDepartment(StringUtils.isBlank(departmentStr) ? StringUtils.EMPTY :
                                departmentStr.startsWith(";") ? departmentStr.substring(1) : departmentStr);
                if (parentPersonnelMap.containsKey(item.getCurrentNodeId())) {
                    Personnel personnel = (Personnel) parentPersonnelMap.get(item.getCurrentNodeId());
                    BeanUtil.copyProperties(personnel, orgExcelRow);
                }
                orgExcelRowList.add(orgExcelRow);
            } else if (Company.TYPE.equals(item.getType())) {
                buildOrgRow(orgMap, item.getDisplayName(), departmentStr, item.getOid(), orgExcelRowList,
                        parentPersonnelMap);
            } else {
                buildOrgRow(orgMap, companyName, departmentStr.concat(String.format(Locale.ENGLISH, ";%s",
                        item.getDisplayName())),
                        item.getOid(),
                        orgExcelRowList, parentPersonnelMap);
            }
        });
    }

//    @Override  //测试使用
//    public String importExcel(MultipartFile file) {
//        // 文件类型校验
//        String originalFilename = file.getOriginalFilename();
//        originalFilename = originalFilename.toLowerCase();
//        Assert.isTrue((originalFilename.endsWith("xlsx") || originalFilename.endsWith("xls")),"import.file.format");
//        // 读数据
//        List<OrgExcelRow> excelRows;
//        try {
//            excelRows = ExcelUtils.read(file.getInputStream(), OrgExcelRow.class);
//        } catch (IOException e) {
//            throw new JWIServiceException("Excel file parse error:" + e.getMessage());
//        }
//        Assert.notEmpty(excelRows, "import.file.blank");
//
//        List<ADDomainRefreshDTO> rows=new ArrayList<>();
//        for (OrgExcelRow row : excelRows) {
//            ADDomainRefreshDTO dto = BeanUtil.copyProperties(row, new ADDomainRefreshDTO());
//            dto.setCompanyCode(row.getCompanyName());
//            if(StringUtil.isNotBlank(row.getDepartment())){
//                dto.setDepCode(Arrays.asList(row.getDepartment().split(";")));
//            }
//            dto.setDisplayName(dto.getName());
//            rows.add(dto);
//        }
//
//        String tipStr = this.synchLdapOrg(rows);
//
//        return "导入组织架构成功;"+tipStr;
//    }

    @Override
    public String importExcel(MultipartFile file) {
        // 文件类型校验
        String originalFilename = file.getOriginalFilename();
        originalFilename = originalFilename.toLowerCase();
        Assert.isTrue((originalFilename.endsWith("xlsx") || originalFilename.endsWith("xls")),"import.file.format");
        // 读数据
        List<OrgExcelRow> rows;
        try {
            rows = ExcelUtils.read(file.getInputStream(), OrgExcelRow.class);
        } catch (IOException e) {
            throw new JWIServiceException("Excel file parse error:" + e.getMessage());
        }
        Assert.notEmpty(rows, "import.file.blank");
        List<String> errorMessage = new ArrayList<>();
        Company rootCompany = getRootCompany();
        // 岗位与人员关联
        Map<ImportTreeNode, Personnel> positionIdPersonnelMap = new HashMap<>();
        // 人员工号统一缓存
        Map<String, Personnel> personnelNumberMap = new HashMap<>();
        // 导入对比用根节点
        ImportTreeNode rootImportNode = new ImportTreeNode(rootCompany.getName(), Company.TYPE, rootCompany.getOid());
        // 岗位配置用于校验岗位配置正确性
        Map<String, PositionDef> positionNameConfig =
                commonService.dynamicQuery(PositionDef.TYPE, null, PositionDef.class).stream().collect(Collectors.toMap(key -> key.getName(), t -> t));
        for (int i = 0, rowsSize = rows.size(); i < rowsSize; i++) {
            int lineNum = i + 2;
            OrgExcelRow row = rows.get(i);
            checkDateNotNull(row, lineNum, errorMessage);
            checkDateValid(row, positionNameConfig, lineNum,errorMessage);
            checkDateConsistency(row, personnelNumberMap, lineNum,errorMessage);
            if (!CollectionUtils.isEmpty(errorMessage)) {
                continue;
            }
            // 当前数据归属公司
            ImportTreeNode company;
            if (!rootCompany.getName().equals(row.getCompanyName())) {
                company = addImportNodeChild(row.getCompanyName(), Company.TYPE, rootImportNode);
            } else {
                company = rootImportNode;
            }
            // 找出岗位归属部门 或公司
            ImportTreeNode positionParent;
            if (StringUtils.isNotBlank(row.getDepartment())) {
                List<String> orgSet = Arrays.asList(row.getDepartment().split(";"));
                ImportTreeNode nowParent = company;
                for (String departmentName : orgSet) {
                    nowParent = addImportNodeChild(departmentName, Department.TYPE, nowParent);
                }
                positionParent = nowParent;
            } else {
                positionParent = company;
            }
            // 建立岗位与人员归属关系
            if (StringUtils.isNotBlank(row.getPosition())) {
                ImportTreeNode position = addImportPosition(row.getPosition(), lineNum + "", Position.TYPE,
                        positionParent);
                position.setId(lineNum);
                position.setPositionDfOid(positionNameConfig.get(position.getName()).getOid());
                // 需要添加人员则进行 岗位临时id与人员关系建立
                if (StringUtils.isNotBlank(row.getNumber())) {
                    Personnel personnel = personnelNumberMap.get(row.getNumber());
                    if (ObjectUtils.isEmpty(personnel)) {
                        personnel = BeanUtil.copyProperties(row, new Personnel());
                        personnel.setDisplayName(row.getName());
                        personnelNumberMap.put(row.getNumber(), personnel);
                    }
                    positionIdPersonnelMap.put(position, BeanUtil.copyProperties(row, personnel));
                }
            }
        }
        rows.clear();
        // 若校验出现异常则进行错误提示
        Assert.isEmpty(errorMessage, StringUtils.join(errorMessage, ";" + System.getProperty("line.separator")));
        // 填充已存在人员信息
        String conflictMessage = queryExistPersonnelInfo(positionIdPersonnelMap);
        // 与当前结构树对比，将不存在节点按照自上而下创建
        compareAndCreateNode(findOrgTree(TreeBuildType.TYPE_ONLY_ORG.getType()), rootImportNode);
        List<Personnel> updatePersonnelList = Lists.newArrayList();
        // 统一对人员进行创建，并绑定岗位
        positionIdPersonnelMap.entrySet().forEach(item -> {
            // 新增人员场景
            if (StringUtils.isBlank(item.getValue().getOid())) {
                beAspectHelper.createOrgByImportNode(item.getKey(), item.getKey().getParentOid(),
                        item.getKey().getParentType());
                Personnel personnel = personnelHelper.create(createPersonnelCreateDTO(item.getValue(),
                        item.getKey().getOid()));
                item.getValue().setOid(personnel.getOid());
            } else {
                List<String> personnelOidList =
                        personnelHelper.queryByOrgList(Lists.newArrayList(item.getKey().getParentOid()), false,
                                false);
                if (!personnelOidList.contains(item.getValue().getOid())) {
                    beAspectHelper.createOrgByImportNode(item.getKey(), item.getKey().getParentOid(),
                            item.getKey().getParentType());
                    personnelHelper.addTo(createPersonnelAddAnyDTO(item.getValue(), item.getKey().getOid()));
                }
                updatePersonnelList.add(item.getValue());
            }
        });
        updatePersonnelList.stream().map(item -> BeanUtil.copyProperties(item,
                new PersonnelUpdateDTO())).collect(Collectors.toList()).forEach(pel -> personnelHelper.update(pel, true));
        return conflictMessage;
    }

    @Override
    public String refreshADDomainData() {
        List<ADDomainRefreshDTO> rows = adDomainCall.queryAllUser();

        if(CollectionUtil.isEmpty(rows)){
            return "同步Ldap数据为空";
        }
        String tipStr = this.synchLdapOrg(rows);

        return "同步Ldap组织架构成功;"+tipStr;
    }

    private String synchLdapOrg(List<ADDomainRefreshDTO> rows){

        List<String> errorMessage = new ArrayList<>();
        Company rootCompany = getRootCompany();
        // 人员账号唯一标识符统一缓存
        Map<String, Personnel> personnelNumberMap = new HashMap<>();
        SynchronizeOrgVarDTO synchronizeOrgVarDTO = new SynchronizeOrgVarDTO();
        Map<Integer, Personnel> positionIdMapPersonnel = synchronizeOrgVarDTO.getPositionIdPersonnelMap();
        // 导入对比用根节点
        ImportTreeNode rootImportNode = new ImportTreeNode(rootCompany.getName(), Company.TYPE, rootCompany.getOid());
        // 岗位配置用于校验岗位配置正确性
        Map<String, PositionDef> positionNameConfig =
                commonService.dynamicQuery(PositionDef.TYPE, null, PositionDef.class).stream().collect(Collectors.toMap(key -> key.getName(), t -> t));
        for (int i = 0, rowsSize = rows.size(); i < rowsSize; i++) {
            int lineNum = i ;
            ADDomainRefreshDTO row = rows.get(i);
            checkDateNotNull(row, lineNum, errorMessage);
            checkDateValid(row, positionNameConfig, lineNum,errorMessage);
            checkDateConsistency(row, personnelNumberMap, lineNum,errorMessage);
            if (!CollectionUtils.isEmpty(errorMessage)) {
                continue;
            }
            // 当前数据归属公司
            ImportTreeNode company;
            if (!rootCompany.getName().equals(row.getCompanyCode())) {
                company = addImportNodeChild(row.getCompanyName(), Company.TYPE, rootImportNode);
            } else {
                company = rootImportNode;
            }
            // 找出岗位归属部门 或公司
            ImportTreeNode positionParent;
            if (StringUtils.isNotBlank(row.getDepartment())) {
                ImportTreeNode nowParent = company;
                List<String> depCode = row.getDepCode();

                for (String code : depCode) {

                    nowParent = addImportNodeChild(code, Department.TYPE, nowParent);
                }
                positionParent = nowParent;
            } else {
                positionParent = company;
            }
            // 建立岗位与人员归属关系
            if (StringUtils.isNotBlank(row.getPosition())) {
                ImportTreeNode position = addImportPosition(row.getPosition(), lineNum + "", Position.TYPE,
                        positionParent);
                position.setId(lineNum);
                position.setPositionDfOid(positionNameConfig.get(position.getName()).getOid());
                // 需要添加人员则进行 岗位临时id与人员唯一标识符关系建立
                if (StringUtils.isNotBlank(row.getAssociatedAccount())) {
                    Personnel personnel = personnelNumberMap.get(row.getAssociatedAccount());
                    if (ObjectUtils.isEmpty(personnel)) {
                        personnel = BeanUtil.copyProperties(row, new Personnel());
                        personnelNumberMap.put(row.getAssociatedAccount(), personnel);
                    }
                    positionIdMapPersonnel.put(lineNum,personnel);
                }
            }
        }

        rows.clear();
        // 若校验出现异常则进行错误提示
        Assert.isEmpty(errorMessage, StringUtils.join(errorMessage, ";" + System.getProperty("line.separator")));
        String tipStr = queryExistPersonnelInfov2(personnelNumberMap);
        //与当前组织人员对比 收集 删除 更新的人员
        this.compareOrgPersonnels(rootCompany,personnelNumberMap,synchronizeOrgVarDTO);
        personnelNumberMap.clear();

        // 与当前结构树对比，将不存在节点(公司 部门)按照自上而下创建
        //把跟节点直接放进去
        List<ImportTreeNode> shouldBeUpdate = synchronizeOrgVarDTO.getShouldBeUpdate();
        shouldBeUpdate.add(rootImportNode);
        compareAndCreateNodeByCode(findOrgTree(TreeBuildType.TYPE_ONLY_ORG.getType()), rootImportNode,synchronizeOrgVarDTO);

        // 统一对部门下的人员新增 更新 并绑定岗位
        this.updateOrgDepPersonnes(synchronizeOrgVarDTO);

        // 统一处理删除
        this.deleteOrgDepPersonnes(synchronizeOrgVarDTO);
        return tipStr;
    }

    private void compareOrgPersonnels(Company rootCompany, Map<String, Personnel> newNumberPersonnelMap, SynchronizeOrgVarDTO synchronizeOrgVarDTO) {
        long pt = System.currentTimeMillis();
        List<Personnel> shouldBeDeletePersonnel = synchronizeOrgVarDTO.getShouldBeDeletePersonnel();
        List<PersonnelUpdateDTO> shouldBeUpdatePersonnel = synchronizeOrgVarDTO.getShouldBeUpdatePersonnel();
        //获取组织架构所有人员
        List<Personnel> personnelSimpleInfos = personnelHelper.findPersonnelByOrg();
        Map<String, Personnel> personnelMap = personnelSimpleInfos.stream().collect(Collectors.toMap(t -> t.getAssociatedAccount(), t -> t, (v1, v2) -> v2));
        synchronizeOrgVarDTO.setDbAllNumberPersonnelMap(personnelMap);

        Map<String, Personnel> oldNumberPersonnelMap = personnelSimpleInfos.stream().collect(Collectors.toMap(t -> t.getAssociatedAccount(), t -> t,(v1, v2) -> v2));

        for (String account : oldNumberPersonnelMap.keySet()) {
            Personnel oldPersonnel = oldNumberPersonnelMap.get(account);
            Personnel newPersonnel = newNumberPersonnelMap.get(account);
            if(ObjectUtils.isEmpty(newPersonnel)){
                shouldBeDeletePersonnel.add(BeanUtil.copyProperties(oldPersonnel,new Personnel()));
            }else {
                boolean isUpdate = compareTowPersonnel(newPersonnel, oldPersonnel);
                if(isUpdate){
                    newPersonnel.setOid(oldPersonnel.getOid());
                    shouldBeUpdatePersonnel.add(BeanUtil.copyProperties(newPersonnel,new PersonnelUpdateDTO()));
                }
            }
        }
        personnelSimpleInfos.clear();

        logger.info("1.同步组织架构收集需要删除的人员数量{},更新的人员数量{}，耗时{}",shouldBeDeletePersonnel.size(), shouldBeUpdatePersonnel.size(), System.currentTimeMillis()-pt);
    }

    //比较人员对象是否需要更新
    private boolean compareTowPersonnel(Personnel newPersonnel, Personnel oldPersonnel){
        boolean isUpdateFlag=false;
        JSONObject newObj = (JSONObject) JSONObject.toJSON(newPersonnel);
        JSONObject oldObj = (JSONObject) JSONObject.toJSON(oldPersonnel);
        for (String key : personnelComKeys) {
            String nVal = newObj.getString(key);
            String oVal = oldObj.getString(key);
            if(!Objects.equals(nVal,oVal)){
                isUpdateFlag=true;
                continue;
            }
        }
        return isUpdateFlag;
    }

    //对比处理公司 部门
    private void compareAndCreateNodeByCode(TreeNode treeNode, ImportTreeNode importTreeNode,SynchronizeOrgVarDTO synchronizeOrgVarDTO) {
        if (importTreeNode.getChildTypeNameMap().isEmpty()) {
            return;
        }
        Map<String, Map<String, TreeNode>> typeNameMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(treeNode)) {
            treeNode.getChildren().stream().forEach(item -> {
                TreeAbleEx current = item.getCurrent();
                String code = current.getCode();
                if(StringUtil.isBlank(code)){
                    code= current.getName();
                }
                typeNameMap.computeIfAbsent(current.getType(), key -> Maps.newHashMap()).put(code, item);
            });
        }

        //收集更新的数据
        List<ImportTreeNode> shouldBeUpdate = synchronizeOrgVarDTO.getShouldBeUpdate();
        List<TreeAbleEx> shouldBeUpdateAble = synchronizeOrgVarDTO.getShouldBeUpdateAble();
        //收集新增的数据
        List<ImportTreeNode> shouldBeCreate = synchronizeOrgVarDTO.getShouldBeCreate();
        //收集删除的数据
        List<TreeAbleEx> shouldBeDelete = synchronizeOrgVarDTO.getShouldBeDelete();

        Map<String, Map<String, ImportTreeNode>> importTreeNodeMap = importTreeNode.getChildTypeNameMap();

        typeNameMap.entrySet().forEach(treeNodeEntry->{
            String orgType = treeNodeEntry.getKey();
            if(!Position.TYPE.equals(orgType)){
                Map<String, TreeNode> oldTreeNodeMap = typeNameMap.get(orgType);
                Map<String, ImportTreeNode> newImportTreeNode = importTreeNodeMap.get(orgType);
                if(MapUtil.isEmpty(newImportTreeNode)){
                    Collection<TreeNode> treeNodes = oldTreeNodeMap.values();
                    List<TreeAbleEx> collect = treeNodes.stream().map(t -> t.getCurrent()).collect(Collectors.toList());
                    shouldBeDelete.addAll(collect);
                }else {
                    for (String name : oldTreeNodeMap.keySet()) {
                        TreeNode oldNode = oldTreeNodeMap.get(name);
                        ImportTreeNode newNode = newImportTreeNode.get(name);
                        if(ObjectUtils.isEmpty(newNode)){
                            shouldBeDelete.add(oldNode.getCurrent());
                        }
                    }
                }
            }
        });


        importTreeNode.getChildTypeNameMap().entrySet().forEach(typeEntry -> {
            typeEntry.getValue().entrySet().forEach(nameEntry -> {
                if (Position.TYPE.equals(typeEntry.getKey())) {
//                    beAspectHelper.createOrgByImportNode(nameEntry.getValue(), importTreeNode.getOid(), importTreeNode.getType());
                    //岗位在外面统一处理
                    ImportTreeNode positionNode = nameEntry.getValue();
                    positionNode.setParentOid(importTreeNode.getOid());
                    positionNode.setParentType(importTreeNode.getType());

                } else {
                    Map<String, TreeNode> treeAbleExMap = typeNameMap.get(typeEntry.getKey());
                    if (ObjectUtils.isEmpty(treeAbleExMap) || ObjectUtils.isEmpty(treeAbleExMap.get(nameEntry.getKey()))) {
                        //新增的数据
                        ImportTreeNode importTreeNodeCreate = nameEntry.getValue();
                        importTreeNodeCreate.setParentOid(importTreeNode.getOid());
                        importTreeNodeCreate.setParentType(importTreeNode.getType());

                        shouldBeCreate.add(importTreeNodeCreate);
                        beAspectHelper.createOrgByImportNode(importTreeNodeCreate, importTreeNode.getOid(),
                                importTreeNode.getType());
                        compareAndCreateNodeByCode(null, nameEntry.getValue(),synchronizeOrgVarDTO);
                    } else {
                        TreeAbleEx current = treeAbleExMap.get(nameEntry.getKey()).getCurrent();
                        ImportTreeNode importTreeNodeUpdate = nameEntry.getValue();
                        nameEntry.getValue().setOid(current.getCurrentNodeId());

//                        shouldBeUpdateAble.add(current); //要对比检查
                        shouldBeUpdate.add(nameEntry.getValue());
                        compareAndCreateNodeByCode(treeAbleExMap.get(nameEntry.getKey()), nameEntry.getValue(),synchronizeOrgVarDTO);
                    }
                }
            });
        });
    }



//    @Override
//    public String refreshADDomainData_old() {
//        List<ADDomainRefreshDTO> ldapData = adDomainCall.queryAllUser();
//        if(CollectionUtil.isEmpty(ldapData)){
//            return "ldap数据为空";
//        }
//        List<String> errorMessage = new ArrayList<>();
//        Company rootCompany = getRootCompany();
//        // 岗位与人员关联
//        Map<ImportTreeNode, Personnel> positionIdPersonnelMap = new HashMap<>();
//        // 人员工号统一缓存
//        Map<String, Personnel> personnelNumberMap = new HashMap<>();
//        // 导入对比用根节点
//        ImportTreeNode rootImportNode = new ImportTreeNode(rootCompany.getName(), Company.TYPE, rootCompany.getOid());
//        // 岗位配置用于校验岗位配置正确性
//        Map<String, PositionDef> positionNameConfig =
//                commonService.dynamicQuery(PositionDef.TYPE, null, PositionDef.class).stream().collect(Collectors.toMap(key -> key.getName(), t -> t));
//        for (int i = 0, rowsSize = ldapData.size(); i < rowsSize; i++) {
//            int lineNum = i + 2;
//            OrgExcelRow row = ldapData.get(i);
//            checkDateNotNull(row, lineNum, errorMessage);
//            checkDateValid(row, positionNameConfig, lineNum,errorMessage);
//            checkDateConsistency(row, personnelNumberMap, lineNum,errorMessage);
//            if (!CollectionUtils.isEmpty(errorMessage)) {
//                continue;
//            }
//            // 当前数据归属公司
//            ImportTreeNode company;
//            if (!rootCompany.getName().equals(row.getCompanyName())) {
//                company = addImportNodeChild(row.getCompanyName(), Company.TYPE, rootImportNode);
//            } else {
//                company = rootImportNode;
//            }
//            // 找出岗位归属部门 或公司
//            ImportTreeNode positionParent;
//            if (StringUtils.isNotBlank(row.getDepartment())) {
//                List<String> orgSet = Arrays.asList(row.getDepartment().split(";"));
//                ImportTreeNode nowParent = company;
//                for (String departmentName : orgSet) {
//                    nowParent = addImportNodeChild(departmentName, Department.TYPE, nowParent);
//                }
//                positionParent = nowParent;
//            } else {
//                positionParent = company;
//            }
//            // 建立岗位与人员归属关系
//            if (StringUtils.isNotBlank(row.getPosition())) {
//                ImportTreeNode position = addImportPosition(row.getPosition(), lineNum + "", Position.TYPE,
//                        positionParent);
//                position.setId(lineNum);
//                position.setPositionDfOid(positionNameConfig.get(position.getName()).getOid());
//                // 需要添加人员则进行 岗位临时id与人员关系建立
//                if (StringUtils.isNotBlank(row.getNumber())) {
//                    Personnel personnel = personnelNumberMap.get(row.getNumber());
//                    if (ObjectUtils.isEmpty(personnel)) {
//                        personnel = BeanUtil.copyProperties(row, new Personnel());
//                        personnel.setDisplayName(row.getName());
//                        personnelNumberMap.put(row.getNumber(), personnel);
//                    }
//                    positionIdPersonnelMap.put(position, BeanUtil.copyProperties(row, personnel));
//                }
//            }
//        }
//        ldapData.clear();
//        // 若校验出现异常则进行错误提示
//        Assert.isEmpty(errorMessage, StringUtils.join(errorMessage, ";" + System.getProperty("line.separator")));
//        // 填充已存在人员信息
//        String conflictMessage = queryExistPersonnelInfo(positionIdPersonnelMap);
//        // 与当前结构树对比，将不存在节点按照自上而下创建
//        compareAndCreateNode(findOrgTree(TreeBuildType.TYPE_ONLY_ORG.getType()), rootImportNode);
//        // 检查通过工号未查询到用户信息的 人员账号
//        List<Personnel> updatePersonnelList = Lists.newArrayList();
//        // 统一对人员进行创建，并绑定岗位
//        positionIdPersonnelMap.entrySet().forEach(item -> {
//            // 新增人员场景
//            if (StringUtils.isBlank(item.getValue().getOid())) {
//                // 账号已存在场景也不进行处理 直接跳过
//                beAspectHelper.createOrgByImportNode(item.getKey(), item.getKey().getParentOid(),
//                        item.getKey().getParentType());
//                Personnel personnel = personnelHelper.create(createPersonnelCreateDTO(item.getValue(),
//                        item.getKey().getOid()));
//                item.getValue().setOid(personnel.getOid());
//            } else {
//                List<String> personnelOidList =
//                        personnelHelper.queryByOrgList(Lists.newArrayList(item.getKey().getParentOid()), false,
//                                false);
//                if (!personnelOidList.contains(item.getValue().getOid())) {
//                    beAspectHelper.createOrgByImportNode(item.getKey(), item.getKey().getParentOid(),
//                            item.getKey().getParentType());
//                    personnelHelper.addTo(createPersonnelAddAnyDTO(item.getValue(), item.getKey().getOid()));
//                }
//                updatePersonnelList.add(item.getValue());
//            }
//        });
//        updatePersonnelList.stream().map(item -> BeanUtil.copyProperties(item,
//                new PersonnelUpdateDTO())).collect(Collectors.toList()).forEach(pel -> personnelHelper.update(pel,
//                true));
//        return conflictMessage;
//    }



    private String updateOrgDepPersonnes(SynchronizeOrgVarDTO synchronizeOrgVarDTO){

        List<ImportTreeNode> shouldBeUpdate = synchronizeOrgVarDTO.getShouldBeUpdate();
        //更新公司 部门的名称

        long cdt = System.currentTimeMillis();
        List<TreeAbleEx> shouldBeUpdateAble = synchronizeOrgVarDTO.getShouldBeUpdateAble();
        List<Company> companyList=new ArrayList<>();
        List<Department> departmentList=new ArrayList<>();

        for (TreeAbleEx treeAbleEx : shouldBeUpdateAble) {
            String type = treeAbleEx.getType();
            if(type.equals(Company.TYPE)){
                companyList.add((Company)treeAbleEx);
            }else if(type.equals(Department.TYPE)){
                departmentList.add((Department)treeAbleEx);
            }
        }

        if(CollectionUtil.isNotEmpty(companyList)){
            commonAbilityHelper.doUpdate(companyList);
        }
        if(CollectionUtil.isNotEmpty(departmentList)){
            commonAbilityHelper.doUpdate(departmentList);
        }

        logger.info("2.同步组织架构更新公司 部门基本信息成功，数量：{}，耗时{}",shouldBeUpdateAble.size(), System.currentTimeMillis()-cdt);

        List<ImportTreeNode> shouldBeCreate = synchronizeOrgVarDTO.getShouldBeCreate();
        shouldBeUpdate.addAll(shouldBeCreate);
        Map<Integer, Personnel> positionIdMapPersonnel = synchronizeOrgVarDTO.getPositionIdPersonnelMap();
        Map<String, Personnel> dbAllAccountPersonnelMap = synchronizeOrgVarDTO.getDbAllNumberPersonnelMap();

        //更新当前节点下关联的人员关系
        long dt = System.currentTimeMillis();
        for (ImportTreeNode importTreeNode : shouldBeUpdate) {

            String currentOid = importTreeNode.getOid();
            //查询当前节点下的一级人员  使用缓存
            List<Personnel> dbItemSubPersonnels = personnelHelper.querySubPersonnelByOrgs(Lists.newArrayList(currentOid), false,
                    false);

            Map<String, Personnel> dbItemNmuberMapPersonnel = dbItemSubPersonnels.stream().collect(Collectors.toMap(t -> t.getAssociatedAccount(), t -> t));

            //人员账号->人员
            Map<String, Personnel> exItemAccountMapPersonnel=new HashMap<>();
            //人员账号->岗位
            Map<String, ImportTreeNode> exItemAccountMapPosition=new HashMap<>();

            //获取部门下的岗位
            Map<String, Map<String, ImportTreeNode>> childTypeNameMap = importTreeNode.getChildTypeNameMap();
            Map<String, ImportTreeNode> positionIdNodeMap = childTypeNameMap.get(Position.TYPE);
            if(MapUtil.isEmpty(positionIdNodeMap)){
                continue;
            }
            Collection<ImportTreeNode> subPositions = positionIdNodeMap.values();

            //通过岗位的临时id获取对应的人员
            for (ImportTreeNode node : subPositions) {
                if(positionIdMapPersonnel.containsKey(node.getId())){
                    Personnel personnel = positionIdMapPersonnel.get(node.getId());

                    exItemAccountMapPersonnel.put(personnel.getAssociatedAccount(),personnel);
                    exItemAccountMapPosition.put(personnel.getAssociatedAccount(),node);
                }
            }

            for (String account : dbItemNmuberMapPersonnel.keySet()) {
                Personnel oldItemPersonnel = dbItemNmuberMapPersonnel.get(account);
                Personnel newItemPersonnel = exItemAccountMapPersonnel.get(account);

                if(ObjectUtils.isEmpty(newItemPersonnel)){
                    //设置白名单
                    if(iamRetainAccount.contains(account)){
                        continue;
                    }
                    //如果人员只在该部门下 就失效
                    //如果人员在多个部门下 当前部门就只断开关系
//                    List<Department> deps = departmentService.findDepsByPersonnel(oldItemPersonnel.getOid());
//                    if(deps.size()>1){
//                        Position position = this.findPositionInDep(oldItemPersonnel, importTreeNode);
//                        this.deleteBelongPandP(oldItemPersonnel,position);
//                    }

                    Position position = this.findPositionInDep(oldItemPersonnel, importTreeNode);
                    commonService.delete(position.getType(),position.getOid());
                }
            }

            for (String account : exItemAccountMapPersonnel.keySet()) {
                Personnel oldItemPersonnel = dbItemNmuberMapPersonnel.get(account);
                Personnel newItemPersonnel = exItemAccountMapPersonnel.get(account);

                ImportTreeNode positionNode = exItemAccountMapPosition.get(account);
                if(ObjectUtils.isEmpty(oldItemPersonnel)){
                    //更新（部门新增人员）
                    //新增人员需要先新增岗位 如果部门下有对应的空岗位 无需创建
                    List<Position> noBindPersonnelByDef = positionService.findNoBindPositionByDef(currentOid, positionNode.getPositionDfOid());
                    Position existPosition = noBindPersonnelByDef.stream().findFirst().orElse(null);
                    if(ObjectUtils.isEmpty(existPosition)){
                        beAspectHelper.createOrgByImportNode(positionNode, positionNode.getParentOid(),
                                positionNode.getParentType());
                    }else {
                        positionNode.setOid(existPosition.getOid());
                    }

                    Personnel dbPersonnel = dbAllAccountPersonnelMap.get(account);

                    if(ObjectUtils.isEmpty(dbPersonnel)){
                        personnelHelper.create(createPersonnelCreateDTO(newItemPersonnel,
                                positionNode.getOid()));
                    }else {

                        personnelHelper.addTo(createPersonnelAddAnyDTO(dbPersonnel, positionNode.getOid()));

                        //如果人员已经失效 要激活
                        if(dbPersonnel.isInvalidFlag()){
                            this.activeDBPersonnel(dbPersonnel);
                        }
                    }

                }else {
                    //更新 (部门内换岗位)
                    Position position = this.findPositionInDep(oldItemPersonnel, importTreeNode);
                    if(!position.getPositionDefOid().equals(positionNode.getPositionDfOid())){
                        this.deleteBelongPandP(oldItemPersonnel,position);
                        //新增人员需要先新增岗位 如果部门下有对应的空岗位 无需创建
                        List<Position> noBindPersonnelByDef = positionService.findNoBindPositionByDef(currentOid, positionNode.getPositionDfOid());
                        Position existPosition = noBindPersonnelByDef.stream().findFirst().orElse(null);
                        if(ObjectUtils.isEmpty(existPosition)){
                            beAspectHelper.createOrgByImportNode(positionNode, positionNode.getParentOid(),
                                    positionNode.getParentType());
                        }else {
                            positionNode.setOid(existPosition.getOid());
                        }

                        //部门下不能同人多岗
                        personnelHelper.addTo(createPersonnelAddAnyDTO(oldItemPersonnel, positionNode.getOid()));
                    }
                }
            }
        }

        logger.info("3.同步组织架构更新部门人员关系信息成功，数量：{}，耗时{}",shouldBeUpdate.size(), System.currentTimeMillis()-dt);
        shouldBeUpdate.clear();
        positionIdMapPersonnel.clear();
        dbAllAccountPersonnelMap.clear();
        //更新人员
        long pt = System.currentTimeMillis();
        List<PersonnelUpdateDTO> shouldBeUpdatePersonnel = synchronizeOrgVarDTO.getShouldBeUpdatePersonnel();
        for (PersonnelUpdateDTO personnelUpdateDTO : shouldBeUpdatePersonnel) {
            personnelHelper.update(personnelUpdateDTO, true);
        }

        logger.info("4.同步组织架构更新人员基本信息成功，数量：{}，耗时{}",shouldBeUpdatePersonnel.size(), System.currentTimeMillis()-pt);
        shouldBeUpdatePersonnel.clear();

        return "";
    }

    private void deleteOrgDepPersonnes(SynchronizeOrgVarDTO synchronizeOrgVarDTO){

        //删除公司 部门
        long sd = System.currentTimeMillis();
        List<TreeAbleEx> shouldBeDelete = synchronizeOrgVarDTO.getShouldBeDelete();
        for (TreeAbleEx current : shouldBeDelete) {
            companyService.deleteSubNodeWithLink(current.getType(),current.getOid());
        }

        logger.info("5.同步组织架构删除的公司 部门，数量：{}，耗时{}",shouldBeDelete.size(), System.currentTimeMillis()-sd);

        //失效人员
//        long sp = System.currentTimeMillis();
//        List<Personnel> shouldBeDeletePersonnel = synchronizeOrgVarDTO.getShouldBeDeletePersonnel();
//        shouldBeDeletePersonnel=shouldBeDeletePersonnel.stream().filter(t->!t.isInvalidFlag()).collect(Collectors.toList());
//        for (Personnel personnel : shouldBeDeletePersonnel) {
//            PersonnelInvalidDTO personnelInvalidDTO = new PersonnelInvalidDTO();
//            personnelInvalidDTO.setPersonnelOid(personnel.getOid());
//            personnelHelper.invalid(personnelInvalidDTO);
//        }
//        logger.info("6.同步组织架构失效的的人员，数量：{}，耗时{}",shouldBeDeletePersonnel.size(), System.currentTimeMillis()-sp);

        //删除空岗位
    }

    //查询人员在当前部门下的岗位
    private Position findPositionInDep(Personnel oldPersonnel, ImportTreeNode importTreeNode){
        SubFilter filter = new SubFilter();
        filter.setFromType(Personnel.TYPE);
        filter.setFromOid(oldPersonnel.getOid());
        filter.setType(BelongTo.TYPE);
        filter.setToType(Position.TYPE);
        if (StringUtil.isNotBlank(importTreeNode.getOid())) {
            filter.setFilter(Condition.where("parentOid").eq(importTreeNode.getOid()));
        }
        List<Position> positions = commonService.dynamicQueryByFrom(filter, Position.class);
        List<Position> one = positions.stream().filter(t -> t.getParentOid().equals(importTreeNode.getOid())).collect(Collectors.toList());

        Assert.isFalse(one.size()>1,String.format("名称为：%s 的人员在部门 %s 下存在多个,请检查数据。",oldPersonnel.getName(),importTreeNode.getName()));
        Assert.notNull(one.size()==0, String.format("工号为：%s 的人员在部门 %s 下不存在,请检查数据。",oldPersonnel.getNumber(),importTreeNode.getName()));
        return one.get(0);

    }

    //查询人员的所有部门
    private Position findPersonnelInDep(Personnel oldPersonnel, ImportTreeNode importTreeNode){
        SubFilter filter = new SubFilter();
        filter.setFromType(Personnel.TYPE);
        filter.setFromOid(oldPersonnel.getOid());
        filter.setType(BelongTo.TYPE);
        filter.setToType(Position.TYPE);
        List<Position> positions = commonService.dynamicQueryByFrom(filter, Position.class);


        List<Position> one = positions.stream().filter(t -> t.getParentOid().equals(importTreeNode.getOid())).collect(Collectors.toList());

        Assert.isFalse(one.size()>1,String.format("名称为：%s 的人员在部门 %s 下存在多个,请检查数据。",oldPersonnel.getName(),importTreeNode.getName()));
        Assert.notNull(one.size()==0, String.format("工号为：%s 的人员在部门 %s 下不存在,请检查数据。",oldPersonnel.getNumber(),importTreeNode.getName()));
        return one.get(0);

    }




    //删除岗位和人员的关系
    private void deleteBelongPandP(Personnel oldPersonnel,Position position){
        BelongTo belongTo = new BelongTo();
        belongTo.setFromType(Personnel.TYPE);
        belongTo.setFromOid(oldPersonnel.getOid());
        belongTo.setToType(Position.TYPE);
        belongTo.setToOid(position.getOid());
        belongTo.setOid("");
        commonService.deleteRelation(belongTo);
    }

    //激活人生
    private void activeDBPersonnel(Personnel oldPersonnel){
        oldPersonnel.setInvalidFlag(false);
        commonService.update(oldPersonnel);
        //删除人员和部门的 INVALID_TO关系
        InValidTo inValidTo = new InValidTo();
        inValidTo.setFromType(Personnel.TYPE);
        inValidTo.setFromOid(oldPersonnel.getOid());
        inValidTo.setToType("");
        inValidTo.setOid("");
        Long count = commonService.deleteRelation(inValidTo);
    }


    private String queryExistPersonnelInfo(Map<ImportTreeNode, Personnel> positionIdPersonnelMap) {
        Map<String, Personnel> numberMap = commonService.dynamicQuery(Personnel.TYPE,
                Condition.where("number").in(positionIdPersonnelMap.values().stream().map(Personnel::getNumber).collect(Collectors.toList())), Personnel.class).stream().collect(Collectors.toMap(item -> item.getNumber(), t -> t));

        Map<String, Personnel> accountMap = commonService.dynamicQuery(Personnel.TYPE,
                Condition.where("associatedAccount").in(positionIdPersonnelMap.values().stream().map(Personnel::getAssociatedAccount).collect(Collectors.toList())), Personnel.class).stream().collect(Collectors.toMap(item -> item.getAssociatedAccount(), t -> t));
        List<String> errorMessageList = new ArrayList<>();
        for (Iterator<Map.Entry<ImportTreeNode, Personnel>> iterator = positionIdPersonnelMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<ImportTreeNode, Personnel> entry = iterator.next();
            Personnel importPersonnel = entry.getValue();
            if(numberMap.containsKey(importPersonnel.getNumber())) {
                Personnel numberExistPersonnel = numberMap.get(importPersonnel.getNumber());
                if(!StringUtils.equals(numberExistPersonnel.getAssociatedAccount(),
                        importPersonnel.getAssociatedAccount())) {
                    errorMessageList.add(buildConflictMessage(true,importPersonnel.getNumber(),
                            numberExistPersonnel.getAssociatedAccount(),importPersonnel.getAssociatedAccount()));
                    iterator.remove();
                    continue;
                } else {
                    importPersonnel.setOid(numberExistPersonnel.getOid());
                }
            }
            if(accountMap.containsKey(importPersonnel.getAssociatedAccount())) {
                Personnel accountExistPersonnel = accountMap.get(importPersonnel.getAssociatedAccount());
                if(!StringUtils.equals(accountExistPersonnel.getNumber(),
                        importPersonnel.getNumber())) {
                    errorMessageList.add(buildConflictMessage(false,importPersonnel.getAssociatedAccount(),
                            accountExistPersonnel.getNumber(),importPersonnel.getNumber()));
                    iterator.remove();
                } else {
                    importPersonnel.setOid(accountExistPersonnel.getOid());
                }
            }
        }
        return StringUtils.join(errorMessageList,",");
    }


    private String queryExistPersonnelInfov2(Map<String, Personnel> personnelNumberMap) {
        Map<String, Personnel> numberMap = commonService.dynamicQuery(Personnel.TYPE,
                Condition.where("number").in(personnelNumberMap.values().stream().map(Personnel::getNumber).collect(Collectors.toList())), Personnel.class).stream().collect(Collectors.toMap(item -> item.getNumber(), t -> t));

        List<String> errorMessageList = new ArrayList<>();
        Map<String, Personnel> personnelOldNumberMap = new HashMap<>();
        Set<Map.Entry<String, Personnel>> entries = personnelNumberMap.entrySet();
        Iterator<Map.Entry<String, Personnel>> iterator = entries.iterator();
        while (iterator.hasNext()){
            Map.Entry<String, Personnel> entry = iterator.next();
            Personnel importPersonnel = entry.getValue();
            if(numberMap.containsKey(importPersonnel.getNumber())) {
                Personnel numberExistPersonnel = numberMap.get(importPersonnel.getNumber());
                String newAccount = importPersonnel.getAssociatedAccount();
                String oldAccount = numberExistPersonnel.getAssociatedAccount();
                if(!StringUtils.equals(oldAccount,
                        newAccount)) {
                    errorMessageList.add(buildConflictMessage(true,importPersonnel.getNumber(),
                            oldAccount, newAccount));
                    //将旧账号回填到新数据 假装没有修改账号
                    iterator.remove();
                    personnelOldNumberMap.put(oldAccount,importPersonnel);
                    importPersonnel.setAssociatedAccount(oldAccount);
                    continue;
                }
            }
        }
        personnelNumberMap.putAll(personnelOldNumberMap);
        return StringUtils.join(errorMessageList,",");
    }

    private static final String CONFLICT_ERROR_TEMPLATE = "导入数据%1$s:%3$s %2$s:%5$s 与现有%2$s:%4$s 冲突";

    private String buildConflictMessage(boolean accountConflict,String queryData,String existData,String importData) {
        String from;
        String to;
        if(accountConflict) {
            from = "工号";
            to = "账号";
        } else {
            from = "账号";
            to = "工号";
        }
        return String.format(CONFLICT_ERROR_TEMPLATE, from, to, queryData, existData, importData);
    }


    // 人员数据一致性校验
    private void checkDateConsistency(OrgExcelRow row, Map<String, Personnel> personnelNumberMap,int lineNum,
                                   List<String> errorMessage) {
        if (StringUtils.isNotBlank(row.getNumber()) && personnelNumberMap.containsKey(row.getNumber())) {
            Personnel personnel = personnelNumberMap.get(row.getNumber());
            if (!StringUtils.equals(row.getEmail(), personnel.getEmail()) || !StringUtils.equals(row.getPhone(),
                    personnel.getPhone())) {
                errorMessage.add(String.format(Locale.ENGLISH, "工号:%s %s:%s", personnel.getNumber(),
                        I18nUtils.getMessage("import.parse.personnel.discrepancy"), lineNum));
            }
        }
    }

    //正确性校验
    private void checkDateValid(OrgExcelRow row, Map<String, PositionDef> positionNameConfig,int lineNum,
                             List<String> errorMessage) {
        if (StringUtils.isNotBlank(row.getDepartment())) {
            List<String> orgList = Arrays.asList(row.getDepartment().split(";"));
            for (String departmentName : orgList) {
                if (StringUtils.isBlank(departmentName)) {
                    errorMessage.add(String.format(Locale.ENGLISH, "%s:%s", I18nUtils.getMessage("import.parse.department.blank"),lineNum));
                }
            }
        }
        if (StringUtils.isNotBlank(row.getPosition())) {
            if (!positionNameConfig.containsKey(row.getPosition())) {
                errorMessage.add(String.format(Locale.ENGLISH, "%s:%s",
                        I18nUtils.getMessage("import.parse.position.config.blank"), lineNum));
            }
        }
    }

    // 必填校验
    private void checkDateNotNull(OrgExcelRow row, int lineNum,List<String> errorMessage) {
        // 公司名称不能为空
        if (StringUtils.isBlank(row.getCompanyName())) {
            errorMessage.add(String.format(Locale.ENGLISH, "%s:%s",
                    I18nUtils.getMessage("import.parse.company.blank"), lineNum));
        }
        // 若有人员信息 岗位名称不能为空
        if (StringUtils.isNotBlank(row.getName()) || StringUtils.isNotBlank(row.getNumber())) {
            if(StringUtils.isBlank(row.getPosition())) {
                errorMessage.add(String.format(Locale.ENGLISH, "%s:%s",
                        I18nUtils.getMessage("import.parse.position.blank"), lineNum));
            }
            // StringUtils.isBlank(row.getPhone()) || StringUtils.isBlank(row.getEmail())
            if (StringUtils.isBlank(row.getName()) || StringUtils.isBlank(row.getNumber()) || StringUtils.isBlank(row.getAssociatedAccount())) {
                errorMessage.add(String.format(Locale.ENGLISH, "%s:%s",
                        I18nUtils.getMessage("import.parse.personnel.blank"), lineNum));
            }
        }
    }

    private void compareAndCreateNode(TreeNode treeNode, ImportTreeNode importTreeNode) {
        if (importTreeNode.getChildTypeNameMap().isEmpty()) {
            return;
        }
        Map<String, Map<String, TreeNode>> typeNameMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(treeNode)) {
            treeNode.getChildren().stream().forEach(item -> {
                typeNameMap.computeIfAbsent(item.getCurrent().getType(), key -> Maps.newHashMap()).put(item.getCurrent().getName(), item);
            });
        }
        importTreeNode.getChildTypeNameMap().entrySet().forEach(typeEntry -> {
            typeEntry.getValue().entrySet().forEach(nameEntry -> {
                if (Position.TYPE.equals(typeEntry.getKey())) {
                    nameEntry.getValue().setParentOid(importTreeNode.getOid());
                    nameEntry.getValue().setParentType(importTreeNode.getType());
                } else {
                    Map<String, TreeNode> treeAbleExMap = typeNameMap.get(typeEntry.getKey());
                    // 无此类型子元素则直接创建 下层也直接创建
                    if (ObjectUtils.isEmpty(treeAbleExMap) || ObjectUtils.isEmpty(treeAbleExMap.get(nameEntry.getKey()))) {
                        beAspectHelper.createOrgByImportNode(nameEntry.getValue(), importTreeNode.getOid(),
                                importTreeNode.getType());
                        compareAndCreateNode(null, nameEntry.getValue());
                    } else {
                        nameEntry.getValue().setOid(treeAbleExMap.get(nameEntry.getKey()).getCurrent().getCurrentNodeId());
                        compareAndCreateNode(treeAbleExMap.get(nameEntry.getKey()), nameEntry.getValue());
                    }
                }
            });
        });
    }

    private PersonnelAddAnyDTO createPersonnelAddAnyDTO(Personnel personnel, String positionOid) {
        PersonnelAddAnyDTO personnelAddAnyDTO = new PersonnelAddAnyDTO();
        personnelAddAnyDTO.setPersonnelOid(personnel.getOid());
        personnelAddAnyDTO.setPositionNumber(positionOid);
        return personnelAddAnyDTO;
    }

    private PersonnelCreateDTO createPersonnelCreateDTO(Personnel personnel,String positionOid) {
        PersonnelCreateDTO personnelCreateDTO = BeanUtil.copyProperties(personnel,new PersonnelCreateDTO());
        personnelCreateDTO.setPositionNumber(positionOid);
        personnelCreateDTO.setAssociatedAccount(personnelCreateDTO.getAssociatedAccount());
        personnelCreateDTO.setDisplayName(personnel.getName());
        return personnelCreateDTO;
    }

    private ImportTreeNode addImportNodeChild(String name, String type, ImportTreeNode parent) {
        ImportTreeNode child = parent.getChild(type, name);
        if (ObjectUtils.isEmpty(child)) {
            child = new ImportTreeNode(name, type);
            parent.addChild(type, name, child);
        }
        return child;
    }

    private ImportTreeNode addImportPosition(String name, String id, String type, ImportTreeNode parent) {
        ImportTreeNode child = parent.getChild(type, id);
        if (ObjectUtils.isEmpty(child)) {
            child = new ImportTreeNode(name, type);
            parent.addChild(type, id, child);
        }
        return child;
    }
}

