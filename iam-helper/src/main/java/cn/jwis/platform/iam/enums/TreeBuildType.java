package cn.jwis.platform.iam.enums;

/**
 * 用于区分组织架构树不同数据展示结构用枚举
 *
 * <AUTHOR>
 */
public enum TreeBuildType {

    /**
     * 用于IAM自身组织结构树，只展示已有人员岗位
     */
    TYPE_NOT_EMPTY_POSITION(1,"已分配人员岗位"),
    /**
     * 用于分配岗位时使用，只展示未分配人员岗位
     */
    TYPE_EMPTY_POSITION(2,"未分配人员岗位"),
    /**
     * 包括所有岗位信息
     */
    TYPE_ALL_POSITION(3,"所有岗位"),
    /**
     * 只展示公司以及部门信息
     */
    TYPE_ONLY_ORG(4,"只需要部门以及公司"),

    /**
     * 已分配人员且有账号
     */
    TYPE_NOT_EMPTY_POSITION_HAS_ACCOUNT(5,"已分配人员且有账号");

    /**
     * 类型
     */
    private int type;

    /**
     * 说明
     */
    private String des;

    TreeBuildType(int type, String des) {
        this.type = type;
        this.des = des;
    }

    public int getType() {
        return type;
    }

    public String getDes() {
        return des;
    }
}
