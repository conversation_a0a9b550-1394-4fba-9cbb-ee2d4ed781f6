package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/23
 * @Description : 更新分类名称用dto
 */
@Data
public class ProductCategoryUpdateDTO {

    @NotBlank(message = "分类oid不能为空")
    @Description("分类oid")
    private String oid;

    @NotBlank(message = "分类名称不能为空")
    @Description("分类名称")
    private String name;

    @Description("显示名称")
    private String displayName;
}
