package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.i18n.I18nUtils;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.ExcelUtils;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.organization.PositionService;
import cn.jwis.platform.iam.organization.dto.PositionCreateDTO;
import cn.jwis.platform.iam.organization.dto.PositionDeleteDTO;
import cn.jwis.platform.iam.organization.dto.PositionSetUpDTO;
import cn.jwis.platform.iam.organization.dto.PositionUpdateDTO;
import cn.jwis.platform.iam.organization.dto.PositionUpdateSetupDTO;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.response.PositionWithRel;
import cn.jwis.platform.iam.relation.BelongTo;
import cn.jwis.platform.iam.response.PositionExcelRow;
import cn.jwis.platform.iam.response.SetUpWithVacancy;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 */
@Service
@Transactional
public class PositionHelperImpl implements PositionHelper {
    protected static final Class<PositionDef> POJO_CLASS = PositionDef.class;

    @Resource
    PositionService positionService;

    @Resource
    CommonService commonService;

    @Resource
    CommonAbilityHelper commonAbilityHelper;

    @Resource
    BeAspectHelper beAspectHelper;

    @Override
    @RestLock
    public PositionDef create(PositionCreateDTO dto) {
        ValidationUtil.validate(dto);
        String name = dto.getName();
        Assert.isNull(findByName(name), "common.name.repeat");
        if (StringUtil.isBlank(dto.getDisplayName())) {
            dto.setDisplayName(dto.getName());
        }
        // 创建 岗位
        PositionDef position = BeanUtil.copyProperties(dto, new PositionDef());
        commonAbilityHelper.doCreate(position);
        return position;
    }

    @Override
    public PositionDef update(PositionUpdateDTO dto) {
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        if (StringUtil.isBlank(dto.getDisplayName())) {
            dto.setDisplayName(dto.getName());
        }
        PositionDef byOid = findByOid(oid);
        Assert.notNull(byOid, "position.not.exits");

        PositionDef sameNamePo = findByName(dto.getName());
        Assert.isFalse(!ObjectUtils.isEmpty(sameNamePo) && !sameNamePo.getOid().equals(byOid.getOid()) , "common.name.repeat");

        if (!byOid.getDisplayName().equals(dto.getDisplayName())) {
            beAspectHelper.updatePositionDisplayName(byOid.getOid(), dto.getDisplayName());
        }
        BeanUtil.copyProperties(dto, byOid);
        return commonAbilityHelper.doUpdate(byOid);
    }


    @Override
    public Long delete(String oid, String type) {
        if (StringUtil.isBlank(oid)) {
            return 0L;
        }
        Long aLong = commonService.countInterRelation(type, oid, null);
        Assert.isTrue(aLong == 0, "position.has.bind");
        return commonService.delete(type, oid);
    }

    @Override
    public List<PositionDef> fuzzy(String searchKey) {
        Condition condition = buildSearchCondition(searchKey);
        return commonService.dynamicQuery(PositionDef.TYPE, condition, buildOrder(), POJO_CLASS);
    }

    @Override
    public PageResult<PositionDef> fuzzyPage(PageSimpleDTO dto) {
        Condition condition = buildSearchCondition(dto.getSearchKey());
        return commonService.dynamicQueryPage(PositionDef.TYPE, condition, buildOrder(), dto.getIndex(),
                dto.getSize(), POJO_CLASS);
    }


    private Order buildOrder() {
        return new Order().desc("name");
    }

    @Override
    public PositionDef findByOid(String oid) {
        return commonService.findByOid(PositionDef.TYPE, oid, POJO_CLASS);
    }

    @Override
    public PositionDef findByNumber(String number) {
        return commonService.dynamicQueryOne(PositionDef.TYPE,
                Condition.where("number").eq(number),
                POJO_CLASS);
    }

    @Override
    public PositionDef findByName(String name) {
        return commonService.dynamicQueryOne(PositionDef.TYPE,
                Condition.where("name").eq(name),
                POJO_CLASS);
    }

    @Override
    @RestLock
    public List<Position> createSetUp(PositionSetUpDTO dto,boolean checkRepeat) {
        ValidationUtil.validate(dto);
        int quantity = dto.getQuantity();
        String fromType = dto.getFromType();
        String fromOid = dto.getFromOid();

        checkFromValid(fromType, fromOid);

        PositionDef positionDef = commonService.findByOid(PositionDef.TYPE, dto.getPositionOid(), PositionDef.class);
        Assert.notNull(positionDef, "position.config.not.exits");
        // 查询已关联的岗位是否包含此新增
        FromToFilter filter = new FromToFilter();
        filter.setFromType(Position.TYPE);
        filter.setFromFilter(Condition.where("positionDefOid").eq(positionDef.getOid()));
        filter.setType(BelongTo.TYPE);
        filter.setToFilter(Condition.where("oid").eq(dto.getFromOid()));
        List<Position> existsPositionInCurOrg = commonService.dynamicQueryFrom(filter, Position.class);
        Assert.isFalse(checkRepeat && !CollectionUtils.isEmpty(existsPositionInCurOrg), "position.has.create");

        if (!Collections.isEmpty(existsPositionInCurOrg)) {
            dto.setReportPositionOid(existsPositionInCurOrg.stream().findFirst().get().getReportPositionOid());
        }

        return beAspectHelper.createPositionAndBind(quantity, positionDef, fromOid, fromType,
                dto.getReportPositionOid());
    }

    private void checkFromValid(String fromType, String fromOid) {
        if (Department.TYPE.equals(fromType)) {
            Department department = commonService.findByOid(fromType, fromOid, Department.class);
            Assert.notNull(department, "department.not.exits");
        } else {
            Company company = commonService.findByOid(fromType, fromOid, Company.class);
            Assert.notNull(company, "company.not.exits");
        }
    }


    @Override
    public SetUpWithVacancy updateSetUp(PositionUpdateSetupDTO dto) {
        SetUpWithVacancy result = new SetUpWithVacancy();
        ValidationUtil.validate(dto);
        Assert.notBlank(dto.getPositionOid(), "common.oid.not.blank");

        checkFromValid(dto.getFromType(), dto.getFromOid());

        PositionDef positionDef = commonService.findByOid(PositionDef.TYPE, dto.getPositionOid(), PositionDef.class);

        Assert.isTrue(positionDef != null, "position.config.not.exits");

        FromToFilter filter = new FromToFilter();
        filter.setFromType(Position.TYPE);
        filter.setFromFilter(Condition.where("positionDefOid").eq(positionDef.getOid()));
        filter.setType(BelongTo.TYPE);
        filter.setToFilter(Condition.where("oid").eq(dto.getFromOid()));
        List<Position> existsPositionInCurOrg = commonService.dynamicQueryFrom(filter, Position.class);

        Assert.notEmpty(existsPositionInCurOrg, "position.not.exits.in.department");

        beAspectHelper.updatePositionReport(dto.getReportPositionOid(), existsPositionInCurOrg);

        int quantity = dto.getQuantity();
        // 重新分配岗位数 大于预分配人数 进行新增
        if (quantity > existsPositionInCurOrg.size()) {
            beAspectHelper.createPositionAndBind(quantity - existsPositionInCurOrg.size(), positionDef,
                    dto.getFromOid(), dto.getFromType(),
                    dto.getReportPositionOid());
            // 重新分配岗位数 小于预分配人数 检查重新分配岗位数 是否大于等于已分配人数
        } else if (quantity < existsPositionInCurOrg.size()) {
            Set<String> hasPersonnelOidSet =
                    positionService.findHasHirePositionByFrom(dto.getFromOid(), positionDef.getOid()).stream().map(Position::getOid).collect(Collectors.toSet());
            Assert.isTrue(hasPersonnelOidSet.size() <= quantity, String.format(Locale.ENGLISH, I18nUtils.getMessage("position.update.error"),
                    hasPersonnelOidSet.size()));
            //可删除的为不包含人员的岗位
            List<Position> couldBeDelPositionList =
                    existsPositionInCurOrg.stream().filter(item -> !hasPersonnelOidSet.contains(item.getOid())).collect(Collectors.toList());

            beAspectHelper.deletePositionList(couldBeDelPositionList.subList(0,
                    existsPositionInCurOrg.size() - quantity).stream().map(Position::getOid).collect(Collectors.toList()),
                    Position.TYPE);
        }

        BeanUtil.copyProperties(positionDef, result);
        result.setEstimateQuantity(quantity);
        return result;
    }

    @Override
    public Long deleteSetUp(PositionDeleteDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notBlank(dto.getFromOid(), "common.oid.not.blank");
        Assert.notBlank(dto.getPositionOid(), "position.oid.not.blank");
        // 部门下的岗位是否分配
        FromToFilter filter = new FromToFilter();
        filter.setFromType(Position.TYPE);
        filter.setFromFilter(Condition.where("positionDefOid").eq(dto.getPositionOid()));
        filter.setType(BelongTo.TYPE);
        filter.setToFilter(Condition.where("oid").eq(dto.getFromOid()));
        List<Position> existsPositionInCurOrg = commonService.dynamicQueryFrom(filter, Position.class);

        Assert.isFalse(CollectionUtil.isEmpty(existsPositionInCurOrg), "position.not.exits.in.department");

        FromToFilter personnelFilter = new FromToFilter();
        personnelFilter.setFromType(Personnel.TYPE);
        personnelFilter.setType(BelongTo.TYPE);
        personnelFilter.setToType(Position.TYPE);
        personnelFilter.setToFilter(Condition.where("oid").in(existsPositionInCurOrg.stream().map(Position::getOid).collect(Collectors.toList())));
        List<Personnel> personnelToPositionList = commonService.dynamicQueryFrom(personnelFilter, Personnel.class);
        Assert.isTrue(personnelToPositionList.isEmpty(), "position.has.bind.personnel");
        return beAspectHelper.deletePositionList(existsPositionInCurOrg.stream().map(Position::getOid).collect(Collectors.toList()),
                Position.TYPE);
    }

    @Override
    public PageResult<PositionWithRel> fuzzyByFrom(String fromType, String fromOid, String searchKey, int index,
                                                   int size) {
        return positionService.fuzzyByFrom(fromType, fromOid, buildSearchCondition(searchKey), buildOrder(), index,
                size);
    }

    private Condition buildSearchCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("displayName").contain(searchKey));
        }
        return condition;
    }

    @Override
    public void exportExcel(HttpServletResponse response) throws IOException {
        OutputStream os = new BufferedOutputStream(response.getOutputStream());
        response.setContentType("application/vnd.ms-excel");
        response.addHeader("Content-Disposition", String.format("attachment;filename=%s.xlsx",
                URLEncoder.encode(String.format(Locale.ENGLISH, "JWI_EXPORT_%s", Position.TYPE),
                        "utf-8")));
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        // 人员数据
        List<PositionDef> positionDefs = commonService.dynamicQuery(PositionDef.TYPE, null, buildOrder(),
                PositionDef.class);
        // 构建数据体
        List<PositionExcelRow> rows = Collections.isEmpty(positionDefs) ? Lists.newArrayList() :
                positionDefs.stream().map(item -> BeanUtil.copyProperties(item,
                new PositionExcelRow())).collect(Collectors.toList());
        EasyExcel.write(os, PositionExcelRow.class).sheet(PositionDef.TYPE).doWrite(rows);
    }

    @Override
    public boolean importExcel(MultipartFile file) {
        // 文件类型校验
        String originalFilename = file.getOriginalFilename();
        originalFilename = originalFilename.toLowerCase();
        Assert.isTrue((originalFilename.endsWith("xlsx") || originalFilename.endsWith("xls")),"import.file.format");
        // 读数据
        List<PositionExcelRow> rows = null;
        try {
            rows = ExcelUtils.read(file.getInputStream(), PositionExcelRow.class);
        } catch (IOException e) {
            throw new JWIServiceException("Excel file parse error:"+e.getMessage());
        }

        Assert.notEmpty(rows, "import.file.blank");

        Map<String, PositionExcelRow> importNameMap = rows.stream().collect(Collectors.toMap(PositionExcelRow::getName,
                t -> t));
        Map<String, PositionDef> queryNameMap = commonService.dynamicQuery(PositionDef.TYPE, null, buildOrder(),
                PositionDef.class).stream().collect(Collectors.toMap(PositionDef::getName,
                t -> t));
        List<PositionDef> createList = new ArrayList<>();
        List<PositionDef> updateList = new ArrayList<>();
        importNameMap.entrySet().forEach(item->{
            Assert.notBlank(item.getValue().getName(), "import.parse.position.config.not.blank");
            if (queryNameMap.containsKey(item.getKey())) {
                PositionDef one = queryNameMap.get(item.getKey());
                if (!StringUtils.equals(one.getDescription(),item.getValue().getDescription())) {
                    one.setDescription(item.getValue().getDescription());
                    updateList.add(one);
                }
            } else {
                PositionDef one = new PositionDef();
                one.setOid(OidGenerator.newOid());
                BeanUtil.copyProperties(item.getValue(), one);
                one.setDisplayName(one.getName());
                createList.add(one);
            }
        });

        commonAbilityHelper.doCreate(createList);
        commonAbilityHelper.doUpdate(updateList);
        return true;
    }


}

