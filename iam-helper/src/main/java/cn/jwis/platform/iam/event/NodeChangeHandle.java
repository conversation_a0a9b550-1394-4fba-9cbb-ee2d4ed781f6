package cn.jwis.platform.iam.event;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.platform.iam.constants.RedisConstants;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.product.dto.AccessMultipleProductDTO;
import cn.jwis.platform.iam.product.entity.Product;
import cn.jwis.platform.iam.product.helper.ProductHelper;
import cn.jwis.platform.iam.user.User;
import com.alibaba.excel.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.jsonwebtoken.lang.Collections;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/11/24
 * @Description :节点变化时处理逻辑
 */
@Component
public class NodeChangeHandle {

    private static final Logger logger = LoggerFactory.getLogger(NodeChangeHandle.class);

    @Resource
    RedisTemplate redisTemplate;

    @Resource
    ProductHelper productHelper;

    @Value(value = "${open.redis.cache:false}")
    private boolean openCache;

    public List<BaseEntity> handleEvent(JoinPoint joinPoint, Object returnValue) {

        if (returnValue instanceof Long) {
            logger.info("delete : " + returnValue);
            Object[] args = joinPoint.getArgs();
            return handleDelete(args);
        } else if (returnValue instanceof List) {
            logger.info("List : " + returnValue);
            // 只有更为更新场景存在多返回值变化
            return handlePutList(returnValue);
        } else if (returnValue instanceof BaseEntity) {
            logger.info("BaseEntity : " + returnValue);
            // 包含人员 岗位 公司 部门变化
            return handlePut(returnValue);
        }
        return Lists.newArrayList();
    }

    private List<BaseEntity> handleDelete(Object[] args) {
        String type = (String) args[1];
        if (args[0] instanceof List) {
            List<String> keyList = (List<String>) args[0];
            return handleDeleteList(keyList, type);
        } else {
            String key = (String) args[0];
            BaseEntity baseEntity = handleDelete(key, type);
            List<BaseEntity> result = new ArrayList<>();
            if(!ObjectUtils.isEmpty(baseEntity)) {
                result.add(baseEntity);
            }
            return result;
        }
    }

    private List<BaseEntity> handleDeleteList(List<String> keyList, String type) {
        List<BaseEntity> result = new ArrayList<>();
        keyList.forEach(item->{
            BaseEntity baseEntity = handleDelete(item,type);
            if(!ObjectUtils.isEmpty(baseEntity)) {
                result.add(baseEntity);
            }
        });
        return result;
    }

    private BaseEntity handleDelete(String key, String type) {
        BaseEntity baseEntity = (BaseEntity) redisTemplate.opsForHash().get(RedisConstants.MAP_ORG_KEY_NAME, key);
        if(!ObjectUtils.isEmpty(baseEntity)) {
            redisTemplate.opsForHash().delete(RedisConstants.MAP_ORG_KEY_NAME, key);
        }
        return baseEntity;
    }

    private List<BaseEntity> handlePutList(Object object) {
        List<BaseEntity> baseEntityList = (List<BaseEntity>) object;
        Set<BaseEntity> resultSet = Sets.newHashSet();
        baseEntityList.forEach(item -> {
            resultSet.addAll(handlePut(item));
        });
        return resultSet.stream().collect(Collectors.toList());
    }

    private List<BaseEntity> handlePut(Object object) {
        BaseEntity baseEntity = (BaseEntity) object;
        if (Personnel.TYPE.equals(baseEntity.getType())) {
            return handlePutPersonnel(baseEntity.getOid(), object);
        } else if (User.TYPE.equals(baseEntity.getType())) {
            return Lists.newArrayList((BaseEntity) object);
        } else {
            return Lists.newArrayList(handlePut(baseEntity.getOid(), baseEntity));
        }
    }

    private BaseEntity handlePut(String key, Object v) {
        if (openCache) {
            redisTemplate.opsForHash().put(RedisConstants.MAP_ORG_KEY_NAME, key, v);
        }
        return (BaseEntity) v;
    }

    /**
     * 处理人员场景，存在多岗位同人员场景，区分人员对象更新 或岗位分配
     *
     * @param key 人员oid
     * @param v   对象
     * @return 变化数据
     */
    public List<BaseEntity> handlePutPersonnel(String key, Object v) {
        List<BaseEntity> result = new ArrayList<>();
        Personnel personnel = (Personnel) v;
        // 人员指定岗位 为指定岗位下人员更新

        if (StringUtils.isNotBlank(personnel.getPositionOid())) {
            redisTemplate.opsForHash().put(RedisConstants.MAP_PER_KEY_NAME, personnel.getUniqueId(), personnel);
            result.add(personnel);
            // 人员指定岗位后进行 部门订阅检查进行自动授权
            subscribeDepartmentAndToAccessPersonnel(personnel);
        } else {
            if(openCache) {
                // 人员对象更新 需要寻找在不同岗位下的人员
                Set<String> keySet = redisTemplate.opsForHash().keys(RedisConstants.MAP_PER_KEY_NAME);
                // 过滤出缓存中存在的人员
                keySet = keySet.stream().filter(item -> item.endsWith(key)).collect(Collectors.toSet());
                // 缓存未开启时
                if (keySet.isEmpty()) {
                    return Lists.newArrayList();
                }
                // 人员无效化
                if (personnel.isInvalidFlag()) {
                    redisTemplate.opsForHash().delete(RedisConstants.MAP_PER_KEY_NAME, keySet.toArray());
                    result.add(personnel);
                } else {
                    // 人员信息更新
                    Map<String, Personnel> map = new HashMap<>(keySet.size());
                    keySet.forEach(item -> {
                        Personnel cachePersonnel = (Personnel) redisTemplate.opsForHash().get(RedisConstants.MAP_PER_KEY_NAME, item);
                        cachePersonnel.setName(personnel.getName());
                        cachePersonnel.setDisplayName(personnel.getDisplayName());
                        cachePersonnel.setEmail(personnel.getEmail());
                        cachePersonnel.setPhone(personnel.getPhone());
                        cachePersonnel.setAssociatedAccount(personnel.getAssociatedAccount());
                        map.put(item, cachePersonnel);
                        result.add(cachePersonnel);
                    });
                    redisTemplate.opsForHash().putAll(RedisConstants.MAP_PER_KEY_NAME, map);
                }
            } else {
                result.add(personnel);
                return result;
            }
        }
        return result;
    }

    /**
     * 人员岗位添加后进行判断，是否归属应用订阅部门，若在范围内，则进行自动授权并
     *
     * @param personnel
     */
    private void subscribeDepartmentAndToAccessPersonnel(Personnel personnel) {
        String positionOid = personnel.getPositionOid();
        if (StringUtils.isBlank(positionOid)) {
            logger.info("Personnel add to invalid Position oid {}", positionOid);
            return;
        }

        if (StringUtils.isBlank(personnel.getAssociatedAccount())) {
            logger.info("Personnel Has no account cancel access oid {}", positionOid);
            return;
        }

        List<Product> productList = productHelper.findSubscribeProductListByPositionOid(positionOid);

        if(!Collections.isEmpty(productList)) {
            AccessMultipleProductDTO dto = new AccessMultipleProductDTO();
            dto.setPersonnelOid(personnel.getOid());
            dto.setProductOidList(productList.stream().map(Product::getOid).collect(Collectors.toSet()));
            productHelper.createMultiple(dto);
        }

    }

}
