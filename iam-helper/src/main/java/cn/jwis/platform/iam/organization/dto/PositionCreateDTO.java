package cn.jwis.platform.iam.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 */
@Data
@ApiModel
public class PositionCreateDTO {

    @NotBlank
    @ApiModelProperty("岗位名称")
    private String name;

    @ApiModelProperty("岗位显示名称")
    private String displayName;

    @ApiModelProperty("岗位描述")
    private String description;

    private String number;
}
