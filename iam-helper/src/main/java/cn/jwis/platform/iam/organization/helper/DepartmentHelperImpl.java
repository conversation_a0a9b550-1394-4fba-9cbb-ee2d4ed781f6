package cn.jwis.platform.iam.organization.helper;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.iam.common.CommonService;
import cn.jwis.platform.iam.common.help.MakeTreeHelper;
import cn.jwis.platform.iam.event.NodeChange;
import cn.jwis.platform.iam.event.RestLock;
import cn.jwis.platform.iam.organization.DepartmentService;
import cn.jwis.platform.iam.organization.dto.DepartmentCreateDTO;
import cn.jwis.platform.iam.organization.dto.DepartmentUpdateDTO;
import cn.jwis.platform.iam.organization.dto.FuzzyDeptPageByOrgDTO;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.response.DepartmentWithSubscribe;
import cn.jwis.platform.iam.relation.BelongTo;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 */
@Service
@Transactional
public class DepartmentHelperImpl implements DepartmentHelper {


    protected static final Class<Department> POJO_CLASS = Department.class;

    @Resource
    DepartmentService departmentService;

    @Resource
    CommonService commonService;

    @Resource
    CommonAbilityHelper commonAbilityHelper;

    @Resource
    MakeTreeHelper makeTreeHelper;

    @Override
    @NodeChange(action="create")
    @RestLock
    public Department create(DepartmentCreateDTO dto) {
        ValidationUtil.validate(dto);
        String parentType = dto.getParentType();
        String parentOid = dto.getParentOid();
        checkDepartmentRepeat(parentType, parentOid, dto.getName());
        Department department = BeanUtil.copyProperties(dto, new Department());
        int parentLevel;
        if (Company.TYPE.equals(parentType)) {
            Company byOid = commonService.findByOid(parentType, parentOid, Company.class);
            parentLevel = byOid.getLevel();
            Assert.notNull(byOid, "company.not.exits");
        } else {
            Department byOid = commonService.findByOid(parentType, parentOid, Department.class);
            Assert.notNull(byOid, "department.not.exits");
            parentLevel = byOid.getLevel();
        }
        department.setLevel(parentLevel + 1);
        department.setParentOid(parentOid);
        department.setParentType(parentType);
        commonAbilityHelper.doCreate(department);
        // 绑定到parent  自身--belongTo-->parent
        BelongTo belongTo = new BelongTo(Department.TYPE, department.getOid(), parentType, parentOid);
        commonService.createRelation(belongTo);
        return department;
    }
    private void checkDepartmentRepeat(String type,String oid,String name) {
        FromToFilter departmentFilter = new FromToFilter();
        departmentFilter.setFromType(Department.TYPE);
        departmentFilter.setType(BelongTo.TYPE);
        departmentFilter.setToType(type);
        departmentFilter.setToFilter(Condition.where("oid").eq(oid));

        Set<String> childNameSet =
                commonService.dynamicQueryFrom(departmentFilter,Department.class).stream().map(Department::getName).collect(Collectors.toSet());
        Assert.isFalse(childNameSet.contains(name),"department.name.repeat");
    }

    @Override
    @NodeChange(action="update")
    public Department update(DepartmentUpdateDTO dto) {
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        Department byOid = findByOid(oid);
        Assert.notNull(byOid, "department.not.exits");
        BeanUtil.copyProperties(dto, byOid);
        return commonAbilityHelper.doUpdate(byOid);
    }

    @Override
    @NodeChange(action="delete")
    public Long delete(String oid,String type) {
        if (StringUtil.isBlank(oid)) {
            return 0L;
        }
        Long aLong = commonService.countInterRelation(type, oid, null);
        Assert.isTrue(aLong == 0, "department.has.bind");
        return commonService.delete(type, oid);
    }

    @Override
    public List<Department> fuzzy(String searchKey) {
        Condition condition = buildSearchCondition(searchKey);
        return commonService.dynamicQuery(Department.TYPE, condition, buildOrder(), POJO_CLASS);
    }

    @Override
    public PageResult<Department> fuzzyPage(PageSimpleDTO dto) {
        Condition condition = buildSearchCondition(dto.getSearchKey());
        return commonService.dynamicQueryPage(Department.TYPE, condition, buildOrder(), dto.getIndex(), dto.getSize()
                , POJO_CLASS);
    }

    @Override
    public PageResult<DepartmentWithSubscribe> fuzzyPageWithSubscribe(FuzzyDeptPageByOrgDTO dto) {
        Condition condition = buildSearchCondition(dto.getSearchKey());
        PageResult<DepartmentWithSubscribe> pageResult = departmentService.fuzzyPageByOrgWithSubscribe(dto.getOrgOid(), condition, dto.getIndex(), dto.getSize(),
                buildOrder());
        Map<String, LinkedList<String>> pathMap =
                makeTreeHelper.findNodePath(pageResult.getRows().stream().map(DepartmentWithSubscribe::getOid).collect(Collectors.toSet()));

        pageResult.getRows().forEach(item->{
            item.setPath(StringUtils.join(pathMap.get(item.getOid()), ">"));
        });
        return pageResult;
    }

    @Override
    public Department findByOid(String oid) {
        return commonService.findByOid(Department.TYPE, oid, POJO_CLASS);
    }

    @Override
    public Department findByNumber(String number) {
        return commonService.dynamicQueryOne(Department.TYPE,
                Condition.where("number").eq(number),
                POJO_CLASS);

    }

    @Override
    public Condition buildSearchCondition(String searchKey) {
        Condition condition = null;
        if (StringUtil.isNoneBlank(searchKey)) {
            condition = Condition.where("name").contain(searchKey)
                    .or(Condition.where("number").contain(searchKey))
                    .or(Condition.where("displayName").contain(searchKey));
        }
        return condition;
    }

    private Order buildOrder() {
        return new Order().desc("name");
    }

}
