package cn.jwis.platform.iam.response;

import cn.jwis.platform.iam.structure.TreeAbleEx;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PersonnelWithPosition implements TreeAbleEx {

    public static final String TYPE = "Position";

    private String number;

    private String name;

    private String displayName;

    private String associatedAccount;

    private String phone;

    private String email;

    private String description;

    private String positionOid;

    private String avatar;

    private JSONObject extensionContent;

    private String positionDisplayName;

    private String parentType;

    private String parentOid;

    private String personnelOid;

    private String positionDefOid;

    private String reportPositionOid;

    private String oid;

    private String tenantOid;

    private String userOid;

    @Override
    public String getParentNodeId() {
        return parentOid;
    }

    @Override
    public String getCurrentNodeId() {
        return positionOid;
    }

    @Override
    public String getType() {
        return TYPE;
    }
}

