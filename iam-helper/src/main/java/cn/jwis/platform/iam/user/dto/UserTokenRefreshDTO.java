package cn.jwis.platform.iam.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserTokenRefreshDTO {

    @ApiModelProperty("oid")
    private String oid;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("用户密级")
    private int securityLevel;

    private boolean disabled;

}