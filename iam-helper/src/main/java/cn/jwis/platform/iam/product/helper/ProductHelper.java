package cn.jwis.platform.iam.product.helper;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.iam.personnel.response.PersonnelWithAccessInfo;
import cn.jwis.platform.iam.personnel.response.ProductWithAccessInfo;
import cn.jwis.platform.iam.product.dto.AccessDTO;
import cn.jwis.platform.iam.product.dto.AccessMultipleProductDTO;
import cn.jwis.platform.iam.product.dto.AccessUpdateDTO;
import cn.jwis.platform.iam.product.dto.FuzzProductCategoryDTO;
import cn.jwis.platform.iam.product.dto.FuzzyByProductOidDTO;
import cn.jwis.platform.iam.product.dto.LicenseInfoDTO;
import cn.jwis.platform.iam.product.dto.ProductCategoryCreateDTO;
import cn.jwis.platform.iam.product.dto.ProductCategoryUpdateDTO;
import cn.jwis.platform.iam.product.dto.ProductCreateDTO;
import cn.jwis.platform.iam.product.dto.ProductPageByPersonnelDTO;
import cn.jwis.platform.iam.product.dto.ProductUpdateDTO;
import cn.jwis.platform.iam.product.dto.SubscribeDTO;
import cn.jwis.platform.iam.product.entity.Product;
import cn.jwis.platform.iam.product.entity.ProductCategory;
import cn.jwis.platform.iam.product.entity.ProductLicense;
import cn.jwis.platform.iam.relation.AccessTo;
import cn.jwis.platform.iam.response.AccessInfoWithPath;
import cn.jwis.platform.iam.response.ProductAndLicenseInfo;
import cn.jwis.platform.iam.structure.TreeNode;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ProductHelper {

    Product create(ProductCreateDTO dto);

    Product update(ProductUpdateDTO dto);

    Long delete(String oid);

    /**
     * 人员一次授权多个应用
     *
     * @param dto 应用清单列表与人员
     * @return 成功与否
     */
    boolean createMultiple(AccessMultipleProductDTO dto);

    /**
     * 通过license文件 公钥文件获取应用key
     *
     * @param dto license文件 公钥文件
     * @return 应用key
     */
    String analysisLicenseInfo(LicenseInfoDTO dto);

    /**
     * 部门订阅
     *
     * @param dto 订阅请求
     * @return 订阅信息
     */
    boolean toSubscribe(SubscribeDTO dto);

    /**
     * 取消订阅
     *
     * @param dto 请求
     * @return 结果
     */
    Long cancelSubscribe(SubscribeDTO dto);

    /**
     * 授权人员使用
     *
     * @param dto 订阅请求
     * @return 订阅信息
     */
    boolean toAccess(AccessDTO dto);

    /**
     * 取消人员授权
     *
     * @param dto 授权请求
     * @return 订阅信息
     */
    boolean cancelAccess(AccessDTO dto);

    /**
     * 更新用户授权信息 主要为时间
     *
     * @param dto
     * @return
     */
    boolean updateAccess(AccessUpdateDTO dto);

    /**
     * 模糊分页分类查询
     *
     * @param dto
     * @return
     */
    PageResult<Product> fuzzyPage(FuzzProductCategoryDTO dto);

    /**
     * 模糊搜索分页查询部门订阅信息
     *
     * @param dto 分页查询信息
     * @return 查询结果
     */
    PageResult<AccessInfoWithPath> fuzzySubscribePage(FuzzyByProductOidDTO dto);

    /**
     * 查询所有授权信息
     *
     * @param productOid 应用Oid
     * @return 查询授权关系
     */
    List<AccessTo> queryAccessToList(String productOid);

    /**
     * 模糊搜索人员 授权信息
     *
     * @param dto 分页查询信息
     * @return 查询结果
     */
    PageResult<PersonnelWithAccessInfo> fuzzyAccessPage(FuzzyByProductOidDTO dto);


    /**
     * 更新部门订阅
     *
     * @param accessUpdateDTO 订阅时间信息
     * @return 结果
     */
    boolean updateSubscribe(AccessUpdateDTO accessUpdateDTO);

    /**
     * 查询人员授权应用清单
     *
     * @param dto
     * @return 应用以及授权时间
     */
    PageResult<ProductWithAccessInfo> queryAccessProductByPersonnel(ProductPageByPersonnelDTO dto);

    /**
     * 查询人员未授权应用清单
     *
     * @param dto
     * @return 应用以及授权时间
     */
    PageResult<Product> queryNoAccessProductByPersonnel(ProductPageByPersonnelDTO dto);


    /**
     * 按照树结构查询人员 包含订阅标记
     *
     * @param searchKey  搜索关键字
     * @param productOid 产品oid
     * @return 树节点
     */
    TreeNode queryPersonnelTreeWithAccessFlag(String productOid, String searchKey);

    ProductAndLicenseInfo findByOid(String oid);

    /**
     * 验证用户应用授权
     *
     * @param account    用户名称
     * @param productKey 应用key
     * @return 验证结果
     */
    int verifyAccess(String account, String productKey);

    /**
     * 创建应用分类
     *
     * @param dto 创建用参数
     * @return 分类实体
     */
    ProductCategory createProductCategory(ProductCategoryCreateDTO dto);

    /**
     * 更新应用分类名称
     *
     * @param dto 更新用参数
     * @return 分类实体
     */
    ProductCategory updateProductCategory(ProductCategoryUpdateDTO dto);

    /**
     * 查询应用分类
     *
     * @return 分类清单
     */
    List<ProductCategory> queryCategory();

    /**
     * 删除分类
     *
     * @param categoryOid 分类oid
     * @return 删除数量
     */
    Long deleteProductCategory(String categoryOid);

    /**
     * 更新应用所属分类
     *
     * @param productOid  应用Oid
     * @param categoryOid 分类oid
     * @return 应用信息
     */
    Product updateCategory(String productOid, String categoryOid);

    /**
     * 获取应用license信息
     *
     * @param productOid 应用Oid
     * @return license
     */
    ProductLicense getProductLicense(String productOid);

    /**
     * 批量修改应用分类
     *
     * @param productOidList 应用oid清单
     * @param categoryOid 分类oid可以为空
     * @return 影响数量
     */
    Long updateProductCategory(List<String> productOidList, String categoryOid);

    /**
     * 查询应用授权人员 按照应用key分组
     *
     * @return 查询结果
     */
    Map<String, Set<String>> queryAllAccessPersonnelGroupByProductKey();


    /**
     * 查询应用授权人员 按照应用key分组
     *
     * @param productKey 应用key
     * @return 查询结果
     */
    Set<String> queryAccessPersonnelByProductKey(String productKey);


    /**
     * 通过应用key查询授权人员组织树
     *
     * @param productKey 应用key
     * @return 树结构跟节点
     */
    TreeNode queryTreeByProductKey(String productKey);


    /**
     * 根据岗位查询所属部门的订阅
     * @param positionOid 岗位oid
     * @return 订阅的部门清单
     */
    List<Product> findSubscribeProductListByPositionOid(String positionOid);

    /**
     * 是否已经到期失效
     *
     * @param expirationDate
     * @return 结果
     */
    boolean isExpire(long expirationDate);
}
