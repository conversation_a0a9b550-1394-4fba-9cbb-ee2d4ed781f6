package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class SubscribeDTO {

    @NotBlank
    @Description("应用oid")
    private String productOid;

    @NotEmpty
    @Description("部门oid")
    private Set<String> departmentOidList;

}
