package cn.jwis.platform.iam.common.util;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.web.session.SessionHelper;

import java.util.UUID;

/**
 * 特殊处理 BaseEntity 公共属性
 */
public class BaseEntityUtil {

    /**
     * 设置创建人、创建时间、更新人、更新时间 oid公共属性
     *
     * @param entity
     */
    public static void setCreateAttrs(BaseEntity entity) {
        if (null == entity) {
            return;
        }
        entity.setOid(UUID.randomUUID().toString());
        entity.setCreateDate(System.currentTimeMillis());
        entity.setUpdateDate(System.currentTimeMillis());
        String account = SessionHelper.getCurrentUser().getAccount();
        entity.setType(entity.getClass().getSimpleName());
        entity.setModelDefinition(entity.getClass().getSimpleName());
        entity.setCreateBy(account);
        entity.setUpdateBy(account);
        // 租户Oid先默认为iamDefault
        entity.setTenantOid("iamDefault");
    }

}
