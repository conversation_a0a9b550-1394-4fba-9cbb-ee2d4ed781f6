package cn.jwis.platform.iam.product.dto;

import cn.jwis.framework.base.annotation.Description;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class ProductCreateDTO {

    @NotBlank
    @Description("应用名称")
    private String name;

    @Description("应用显示名称")
    private String displayName;

    @Description("应用描述")
    private String description;

    private String productVersion;

    private String productType;

    private String homePage;

    @NotBlank
    @Description("应用key")
    private String productKey;

    @Description("应用图标")
    private String icon;

    @NotBlank
    @Description("license文件Oid")
    private String licenseOid;

    @NotBlank
    @Description("公钥文件Oid")
    private String publicKeyOid;

    @Description("应用分类oid")
    private String categoryOid;
}
