user.input.incorrect = 请输入正确的用户名密码
user.account.password.empty=用户名密码不能为空
user.disabled=用户账号已禁用
user.input.null=用户名密码不能为空
passwordPolicyRemote.error=密码策略远程调用异常
user.password.update.cycle=距离上次密码设置超过{0}天，请及时更新密码
user.password.lock.tip=账号密码输入到达{0}次，锁定账号{1}分钟，请稍候登录
user.password.lock.tip1=账号被锁定，请联系管理员解锁
oid.input.null=oid为空
user.null=用户不存在
account.null=账号为空
oldPassword.null=原始密码为空
newPassword.null=新密码为空
confirmPassword.null=确认密码为空
newPassword.and.confirmPassword.different=新密码和确认密码不一致
user.not.found=用户未找到
user.oldPassword.error = 原始密码错误
token.overdue=token 已过期，请重新登录！
access.token.invalid=token 不合法
access.token.header=header不包含token信息
app.name.null=appName不能为空





