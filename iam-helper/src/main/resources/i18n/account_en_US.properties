user.input.incorrect = Please enter the correct user name and password
user.account.password.empty=User name and password cannot be empty
user.disabled=User account disabled
user.input.null=User name and password cannot be empty
passwordPolicyRemote.error=Password policy remote call exception
user.password.update.cycle=It is more than {0} days since the last password setting. Please update your password in time
user.password.lock.tip=The account password has been entered {0} times, and the account has been locked for {1} minutes. Please log in later
user.password.lock.tip1=The account is locked. Please contact the administrator to unlock it
oid.input.null=Oid is empty
user.null=user does not exists
account.null=account is empty 
oldPassword.null=Original password is empty
newPassword.null=New password is empty
confirmPassword.null=The confirmed password is empty
newPassword.and.confirmPassword.different=The new password and the confirmed password are inconsistent
user.not.found=user does not exists
user.oldPassword.error = Original password error
token.overdue=The token has expired, please login again!
access.token.invalid=Illegal token
access.token.header=The request header does not contain a token




