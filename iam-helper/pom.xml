<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.jwis.platform.iam</groupId>
        <artifactId>iam-server</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>iam-helper</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>iam-remote-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>iam-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>iam-repo-neo4j</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>config-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>foundation-repo-neo4j</artifactId>
        </dependency>

        <!-- 消息提醒 -->
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>notification-repo-neo4j</artifactId>
        </dependency>


        <!-- 消息中间件 -->
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>msgqueue-client-sdk</artifactId>
        </dependency>



        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.3.10</version>
        </dependency>

    </dependencies>

</project>