package cn.jwis.platform.iam.user;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.auth.helper.DeviceHelper;
import cn.jwis.platform.iam.device.Device;
import cn.jwis.platform.iam.user.dto.UserDTO;
import cn.jwis.platform.iam.user.dto.UserTokenRefreshDTO;
import cn.jwis.platform.iam.user.helper.UserHelper;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "user")
@Api(tags = {"用户服务"}, value = "UserController")
@Slf4j
public class UserController {

    @Autowired
    private UserHelper userHelper;

    @Autowired
    private DeviceHelper deviceHelper;


    @ApiOperation(value = "修改账户信息", notes = "修改账户信息")
    @PostMapping(value = "/update")
    public Result<User> update(@RequestBody UserDTO data) {
        try {

            return Result.success(userHelper.update(data));
        } catch (Exception e) {
            return Result.Fail(-1, e.getMessage());
        }
    }


    @ApiOperation(value = "根据账户获取用户", notes = "根据账户获取用户")
    @GetMapping(value = "/findByAccount")
    public Result<UserDTO> findByAccount(@ApiParam("账户名") @RequestParam String account) {
        return Result.success(userHelper.searchByAccount(account));
    }


    @ApiOperation(value = "根据账户获取用户", notes = "根据账户获取用户")
    @PostMapping(value = "/findByAccounts")
    public Result<List<UserDTO>> findByAccounts(@ApiParam("账户名列表") @RequestBody List<String> accounts) {
        return Result.success(userHelper.searchByAccounts(accounts));
    }



    @GetMapping("/search/condition/oid")
    @ApiOperation(value = "获取用户根据oid")
    public Result<UserDTO> searchByOid(@ApiParam("用户OID") @RequestParam("userOid") String userOid) {
        try {
            return Result.success(userHelper.searchByOid(userOid));
        } catch (Exception e) {
            return Result.Fail(-1, e.getMessage());
        }
    }

    @ApiOperation(value = "根据账户获取用户", notes = "根据账户获取用户")
    @PostMapping(value = "/searchByOidList")
    public Result<List<UserDTO>> searchByOidList(@ApiParam("用户OID列表") @RequestBody List<String> oidList) {
        return Result.success(userHelper.searchByOidList(oidList));
    }

    @ApiOperation(value = "根据用户查询设备", notes = "根据用户关联设备")
    @GetMapping(value = "/findDevice/byAccount")
    public Result<List<Device>> findDeviceByAccount(@ApiParam("账户名") @RequestParam String account) {
        return Result.success(deviceHelper.findDeviceByAccount(account));
    }


    @ApiOperation(value = "获取用户签名", notes = "获取用户签名")
    @PostMapping(value = "/findSignature/byAccount")
    public Result<Map<String, File>> findSignatureByAccount(@ApiParam("账户名称列表") @RequestBody List<String> accounts) {
        return Result.success(userHelper.findSignatureByAccount(accounts));
    }


    @ApiOperation(value = "重新设置密码", notes = "重新设置密码")
    @PostMapping(value = "/reset/password")
    public Result resetPassword(@ApiParam(value = "用户") @RequestParam(name = "userOid") String userOid,
                                @ApiParam(value = "老密码") @RequestParam(name = "oldPassword") String oldPassword,
                                @ApiParam(value = "确认密码") @RequestParam(name = "confirmPassword") String confirmPassword,
                                @ApiParam(value = "新密码") @RequestParam(name = "newPassword") String newPassword) {
        try {
            userHelper.resetPassword(userOid, oldPassword, confirmPassword, newPassword);
            return Result.success();
        } catch (Exception e) {
            return Result.Fail(e.getMessage());
        }
    }

    @ApiOperation(value = "密码超期重新设置密码", notes = "密码超期重新设置密码")
    @PostMapping(value = "/update/password")
    public Result passwordCycleUpdate(@ApiParam(value = "用户") @RequestParam(name = "account") String userOid,
                                      @ApiParam(value = "老密码") @RequestParam(name = "oldPassword") String oldPassword,
                                      @ApiParam(value = "确认密码") @RequestParam(name = "confirmPassword") String confirmPassword,
                                      @ApiParam(value = "新密码") @RequestParam(name = "newPassword") String newPassword) {
        try {
            userHelper.passwordCycleUpdate(userOid, oldPassword, confirmPassword, newPassword);
            return Result.success();
        } catch (Exception e) {
            return Result.Fail(e.getMessage());
        }
    }

    @ApiOperation(value = "初始化密码", notes = "初始化密码")
    @PostMapping(value = "/init/password")
    public Result<UserDTO> initPassword(@ApiParam(value = "用户oid") @RequestParam(name = "userOid") String userOid) {
        try {
            return Result.success(userHelper.initPassword(userOid));
        } catch (Exception e) {
            return Result.Fail(e.getMessage());
        }
    }



    @ApiOperation(value = "校验原始密码是否正确", notes = "校验原始密码是否正确")
    @PostMapping(value = "/validateOldPassword")
    public Result<Boolean> validateOldPassword(@RequestBody UserDTO dto) {
        try {
            boolean correct = userHelper.validateOldPassword(dto);
            return Result.success(correct);
        } catch (Exception e) {
            return Result.Fail(-1, e.getMessage());
        }
    }


    @ApiOperation(value = "刷新用户token的redis缓存", notes = "刷新用户token的redis缓存")
    @PostMapping(value = "/refresh/token-redis")
    public Result refreshTokenRedis(UserTokenRefreshDTO dto) {
        try {
            userHelper.refreshTokenRedis(dto);
            return Result.success();
        } catch (Exception e) {
            return Result.Fail(-1, e.getMessage());
        }
    }


}
