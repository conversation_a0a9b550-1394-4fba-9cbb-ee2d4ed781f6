package cn.jwis.platform.iam.workflow;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.user.User;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> yefei
 * @Date ： 2023/2/16
 * @Description : workflow站位接口无实际业务场景
 */

@RestController
@RequestMapping(value = "workflow-server")
@Api(tags = {"workflow站位接口"}, value = "workflow-server")
@Slf4j
public class WorkFlowController {

    @ApiOperation(value = "workflow站位接口", notes = "workflow站位接口")
    @GetMapping(value = "/workflow/model/deployed/latest/fuzzy")
    public Result<User> update(@RequestParam String searchKey,@RequestParam String containerType,String containerOid) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tenantList", "[]");
            jsonObject.put("userTenantAuthInfo", "{}");
            return Result.success(jsonObject);
        } catch (Exception e) {
            return Result.Fail(e.getMessage());
        }
    }
}
