package cn.jwis.platform.iam.product;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.product.dto.AccessUpdateDTO;
import cn.jwis.platform.iam.product.dto.FuzzyByProductOidDTO;
import cn.jwis.platform.iam.product.dto.SubscribeDTO;
import cn.jwis.platform.iam.product.helper.ProductHelper;
import cn.jwis.platform.iam.response.AccessInfoWithPath;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： Created in 2022/11/2
 * @description : 应用相关管理
 */
@RestController
@RequestMapping("/product/subscribe")
@Api(tags = "应用API", value = "Product Subscribe Controller")
public class ProductSubscribeController {

    @Resource
    ProductHelper productHelper;

    @PostMapping("/create")
    @ApiOperation(response = Result.class, value = "订阅应用", notes = "订阅应用")
    public Result<Boolean> create(@RequestBody SubscribeDTO dto) {
        return Result.success(productHelper.toSubscribe(dto));
    }

    @PostMapping("/delete")
    @ApiOperation(response = Result.class, value = "删除订阅", notes = "删除订阅")
    public Result<Long> delete(@RequestBody SubscribeDTO dto) {
        return Result.success(productHelper.cancelSubscribe(dto));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(response = Result.class, value = "模糊分页查询", notes = "模糊分页查询")
    public Result<PageResult<AccessInfoWithPath>> fuzzyPage(@RequestBody FuzzyByProductOidDTO dto) {
        return Result.success(productHelper.fuzzySubscribePage(dto));
    }

    @PostMapping("/update")
    @ApiOperation(response = Result.class, value = "更新部门订阅信息", notes = "更新部门订阅信息")
    public Result<Boolean> update(@RequestBody AccessUpdateDTO dto) {
        return Result.success(productHelper.updateSubscribe(dto));
    }
}
