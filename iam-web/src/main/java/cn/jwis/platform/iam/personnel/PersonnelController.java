package cn.jwis.platform.iam.personnel;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.personnel.dto.FuzzyPersonnelPageByOrgDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelAddAnyDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelCreateDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelInvalidDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelOnPositionPageDTO;
import cn.jwis.platform.iam.personnel.dto.PersonnelUpdateDTO;
import cn.jwis.platform.iam.personnel.dto.UserDescriptionDTO;
import cn.jwis.platform.iam.personnel.entity.Personnel;
import cn.jwis.platform.iam.personnel.helper.PersonnelHelper;
import cn.jwis.platform.iam.personnel.response.PersonnelSimpleInfo;
import cn.jwis.platform.iam.personnel.response.PersonnelWithUser;
import cn.jwis.platform.iam.response.LevelNodePersonnel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
@RestController
@RequestMapping("/personnel")
@Api(tags = "人员 API", value = "Personnel Controller")
public class PersonnelController {

    @Resource
    PersonnelHelper personnelHelper;

    @PostMapping("/create")
    @ApiOperation(value = "创建人员", notes = "创建人员")
    public Result<Personnel> create(@RequestBody PersonnelCreateDTO dto) {
        return Result.success(personnelHelper.create(dto));
    }

    @PostMapping("/addTo")
    @ApiOperation(value = "添加到指定岗位", notes = "添加到指定岗位")
    public Result<Personnel> addTo(@RequestBody PersonnelAddAnyDTO dto) {
        return Result.success(personnelHelper.addTo(dto));
    }

    @PostMapping("/addToAny")
    @ApiOperation(value = "添加到部门下同类型任意岗位", notes = "添加到部门下同类型任意岗位")
    public Result<Personnel> addToAny(@RequestBody PersonnelAddAnyDTO dto) {
        return Result.success(personnelHelper.addToAny(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新", notes = "更新")
    public Result<Personnel> update(@RequestBody PersonnelUpdateDTO dto) {
        return Result.success(personnelHelper.update(dto,true));
    }

    @PostMapping("/invalid")
    @ApiOperation(value = "无效化成员", notes = "无效化成员")
    public Result<Personnel> invalid(@RequestBody PersonnelInvalidDTO dto) {
        return Result.success(personnelHelper.invalid(dto));
    }

    @GetMapping("/fuzzy")
    @ApiOperation(value = "模糊查询", notes = "模糊查询")
    public Result fuzzy(@ApiParam("搜索关键字") @RequestParam(required = false) String searchKey) {
        return Result.success(personnelHelper.fuzzy(searchKey));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "人员模糊分页查询", notes = "模糊分页查询")
    public Result<List<LevelNodePersonnel>> fuzzyPage(@RequestBody PageSimpleDTO dto) {
        return Result.success(personnelHelper.fuzzyPage(dto));
    }

    @PostMapping("/fuzzyPageNoAccount")
    @ApiOperation(value = "无账号人员模糊分页查询", notes = "无账号人员模糊分页查询")
    public Result<PageResult<Personnel>> fuzzyPageNoAccount(@RequestBody PageSimpleDTO dto) {
        return Result.success(personnelHelper.fuzzyPageNoAccount(dto));
    }

    @GetMapping("/findByOid")
    @ApiOperation(value = "通过OID查询人员", notes = "通过OID查询人员")
    public Result<PersonnelWithUser> findByOid(@ApiParam("人员OID") @RequestParam String oid) {
        return Result.success(personnelHelper.findByOid(oid));
    }

    @GetMapping("/findPosition")
    @ApiOperation(value = "通过人员（可以指定组织）查询其所属的岗位", notes = "通过人员（可以指定组织）查询其所属的岗位")
    public Result<List<PositionDef>> findPosition(
            @ApiParam("所属组织OID") @RequestParam(required = false) String orgOid,
            @ApiParam("人员OID") @RequestParam String oid) {
        return Result.success(personnelHelper.findPosition(orgOid, oid));
    }

    @PostMapping("/findPersonnelPageByPosition")
    @ApiOperation(value = "查询组织下岗位分配人员", notes = "查询组织下岗位分配人员")
    public Result<PageResult<Personnel>> findPersonnelByPosition(
            @RequestBody PersonnelOnPositionPageDTO dto) {
        return Result.success(personnelHelper.findPersonnelPageByPosition(dto));
    }

    @GetMapping("/findByNumber")
    @ApiOperation(value = "通过工号查询人员", notes = "通过工号查询人员")
    public Result<Personnel> findByNumber(@ApiParam("人员工号") @RequestParam String number) {
        return Result.success(personnelHelper.findByNumber(number));
    }

    @GetMapping("/fuzzyByOrg")
    @ApiOperation(value = "模糊查询 公司/部门 下的人员", notes = "模糊查询 公司/部门 下的人员")
    public Result<List<PersonnelSimpleInfo>> fuzzyByOrg(@ApiParam("所属组织类型Company或Department") @RequestParam String orgType,
                                                        @ApiParam("所属组织OID") @RequestParam String orgOid,
                                                        @ApiParam("人员是否无效标识默认为false") @RequestParam(required = false,
                                                                defaultValue = "false") boolean invalidFlag,
                                                        @ApiParam("搜索关键字非必填") @RequestParam(required = false) String searchKey) {
        return Result.success(personnelHelper.fuzzyByOrg(orgType, orgOid, invalidFlag, searchKey));
    }

    @GetMapping("/cntByOrg")
    @ApiOperation(value = "统计 公司/部门 下的人员数量", notes = "统计 公司/部门 下的人员数量")
    public Result<Long> cntByOrg(@ApiParam("所属组织类型Company或Department") @RequestParam String orgType,
                                 @ApiParam("所属组织OID") @RequestParam String orgOid,
                                 @ApiParam("搜索关键字非必填") @RequestParam(required = false) String searchKey) {
        return Result.success(personnelHelper.cntByOrg(orgType, orgOid, searchKey));
    }

    @PostMapping("/fuzzyPageByOrg")
    @ApiOperation(value = "模糊分页查询 公司/部门 下的人员", notes = "模糊分页查询 公司/部门 下的人员")
    public Result<PageResult<PersonnelSimpleInfo>> fuzzyPageByOrg(@RequestBody FuzzyPersonnelPageByOrgDTO dto) {
        return Result.success(personnelHelper.fuzzyPageByOrg(dto));
    }

    @PostMapping("/findPositionsByUserOidList")
    @ApiOperation(value = "通过UserOidList获取岗位列表", notes = "通过UserOidList获取岗位列表")
    public Result<List<PersonnelSimpleInfo>> findPositionsByUserAccount(@ApiParam("账户OID列表") @RequestBody List<String> userOidList) {
        return Result.success(personnelHelper.findPositions(userOidList));
    }

    @PostMapping("/setUserComments")
    @ApiOperation(value = "设置用户备注以及描述", notes = "设置用户备注以及描述")
    public Result<Boolean> setUserComments(@RequestBody UserDescriptionDTO dto) {
        return Result.success(personnelHelper.setUserComments(dto));
    }
}
