package cn.jwis.platform.iam.product;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.product.dto.FuzzProductCategoryDTO;
import cn.jwis.platform.iam.product.dto.LicenseInfoDTO;
import cn.jwis.platform.iam.product.dto.ProductCategoryCreateDTO;
import cn.jwis.platform.iam.product.dto.ProductCategoryUpdateDTO;
import cn.jwis.platform.iam.product.dto.ProductCreateDTO;
import cn.jwis.platform.iam.product.dto.ProductUpdateDTO;
import cn.jwis.platform.iam.product.entity.Product;
import cn.jwis.platform.iam.product.entity.ProductCategory;
import cn.jwis.platform.iam.product.entity.ProductLicense;
import cn.jwis.platform.iam.product.helper.ProductHelper;
import cn.jwis.platform.iam.response.ProductAndLicenseInfo;
import cn.jwis.platform.iam.structure.TreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： Created in 2022/11/2
 * @description : 应用相关管理
 */
@RestController
@RequestMapping("/product")
@Api(tags = "应用API", value = "Product Controller")
public class ProductController {

    @Resource
    ProductHelper productHelper;

    @PostMapping("/create")
    @ApiOperation(value = "创建应用", notes = "创建应用")
    public Result<Product> create(@RequestBody ProductCreateDTO dto) {
        return Result.success(productHelper.create(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新应用", notes = "更新应用")
    public Result<Product> update(@RequestBody ProductUpdateDTO dto) {
        return Result.success(productHelper.update(dto));
    }

    @GetMapping("/updateCategory")
    @ApiOperation(value = "更新应用分类", notes = "更新应用分类")
    public Result<Product> updateCategory(@ApiParam("应用OID") @RequestParam String productOid,
                                          @ApiParam("应用分类OID") @RequestParam String categoryOid) {
        return Result.success(productHelper.updateCategory(productOid,categoryOid));
    }

    @PostMapping("/category/create")
    @ApiOperation(value = "创建应用分类", notes = "创建应用分类")
    public Result<ProductCategory> createProductCategory(@RequestBody ProductCategoryCreateDTO dto) {
        return Result.success(productHelper.createProductCategory(dto));
    }

    @PostMapping("/category/update")
    @ApiOperation(value = "更新应用分类", notes = "更新应用分类")
    public Result<ProductCategory> updateProductCategory(@RequestBody ProductCategoryUpdateDTO dto) {
        return Result.success(productHelper.updateProductCategory(dto));
    }

    @GetMapping("/category/delete")
    @ApiOperation(value = "更新应用分类", notes = "更新应用分类")
    public Result<Long> deleteProductCategory(@ApiParam("应用分类OID") @RequestParam String categoryOid) {
        return Result.success(productHelper.deleteProductCategory(categoryOid));
    }

    @GetMapping("/category/query")
    @ApiOperation(value = "查询应用分类", notes = "查询应用分类")
    public Result<List<ProductCategory>> updateProductCategory() {
        return Result.success(productHelper.queryCategory());
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除应用", notes = "删除应用")
    public Result<Long> delete(@ApiParam("应用OID") @RequestParam String oid) {
        return Result.success(productHelper.delete(oid));
    }

    @PostMapping("/analysisLicenseInfo")
    @ApiOperation(value = "解析license内容", notes = "解析license内容")
    public Result<String> create(@RequestBody LicenseInfoDTO dto) {
        return Result.success(productHelper.analysisLicenseInfo(dto));
    }

    @GetMapping("/queryProductLicense")
    @ApiOperation(value = "更新", notes = "更新")
    public Result<ProductLicense> queryProductLicense(@ApiParam("应用OID") @RequestParam String productOid) {
        return Result.success(productHelper.getProductLicense(productOid));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "模糊分页查询", notes = "模糊分页查询")
    public Result<PageResult<Product>> fuzzyPage(@RequestBody FuzzProductCategoryDTO dto) {
        return Result.success(productHelper.fuzzyPage(dto));
    }

    @GetMapping("/findByOid")
    @ApiOperation(value = "通过oid查询", notes = "通过oid查询")
    public Result<ProductAndLicenseInfo> findByOid(@ApiParam("应用OID") @RequestParam String oid) {
        return Result.success(productHelper.findByOid(oid));
    }

    @GetMapping("/queryTreeByProductKey")
    @ApiOperation(value = "通过应用key查询授权人员组织树", notes = "通过应用key查询授权人员组织树")
    public Result<TreeNode> queryTreeByProductKey(@ApiParam("应用key") @RequestParam String productKey) {
        return Result.success(productHelper.queryTreeByProductKey(productKey));
    }
}
