package cn.jwis.platform.iam.organization;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.iam.organization.dto.CompanyCreateDTO;
import cn.jwis.platform.iam.organization.dto.CompanyUpdateDTO;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.helper.CompanyHelper;
import cn.jwis.platform.iam.response.LevelNodeInfo;
import cn.jwis.platform.iam.structure.TreeNode;
import com.alibaba.excel.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
@RestController
@RequestMapping("/company")
@Api(tags = "公司 API", value = "Company Controller")
public class CompanyController {

    @Resource
    CompanyHelper companyHelper;

    @PostMapping("/create")
    @ApiOperation(value = "创建公司", notes = "创建公司")
    public Result<Company> create(@RequestBody CompanyCreateDTO dto) {
        return Result.success(companyHelper.create(dto));
    }

    @IgnoreRestUrlAccess
    @PostMapping("/createRoot")
    @ApiOperation(value = "创建总公司", notes = "总公司为组织结构根节点")
    public Result<Company> createRoot(@RequestBody CompanyCreateDTO dto) {
        return Result.success(companyHelper.createRoot(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新公司信息", notes = "更新公司信息")
    public Result<Company> update(@RequestBody CompanyUpdateDTO dto) {
        return Result.success(companyHelper.update(dto));
    }

    @GetMapping("/delete")
    @ApiOperation(response = Result.class, value = "删除公司", notes = "删除公司")
    public Result delete(@ApiParam("公司OID") @RequestParam String oid) {
        return Result.success(companyHelper.delete(oid, Company.TYPE));
    }

    @GetMapping("/findByOid")
    @ApiOperation(value = "通过oid查询公司", notes = "通过oid查询公司")
    public Result<Long> findByOid(@ApiParam("公司OID") @RequestParam String oid) {
        return Result.success(companyHelper.findByOid(oid));
    }

    @GetMapping("/fuzzy")
    @ApiOperation(value = "模糊查询公司", notes = "模糊查询公司")
    public Result<List<Company>> fuzzy(@ApiParam("查询关键字非必传") @RequestParam(required = false) String searchKey) {
        return Result.success(companyHelper.fuzzy(searchKey));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "模糊分页查询公司", notes = "模糊分页查询公司")
    public Result<PageResult<Company>> fuzzyPage(@RequestBody PageSimpleDTO dto) {
        return Result.success(companyHelper.fuzzyPage(dto));
    }

    @GetMapping("/findByName")
    @ApiOperation(value = "通过名称查询公司", notes = "通过名称查询公司")
    public Result<Company> findByName(@ApiParam("公司名称") @RequestParam String name) {
        return Result.success(companyHelper.findByName(name));
    }

    @GetMapping("/findOrgTree")
    @ApiOperation(value = "获取组织树", notes = "根据结构类型获取组织树信息 1过滤无人员岗位 2包含无人员岗位 3包含所有岗位 4只包含组织结构 5过滤无人员岗位 无账号人员")
    public Result<TreeNode> findOrgTree(@ApiParam("树结构类型") @RequestParam(required = false, defaultValue = "1") Integer buildType) {
        return Result.success(companyHelper.findOrgTree(buildType));
    }

    @GetMapping("/fuzzyOrgTree")
    @ApiOperation(value = "模糊查询组织树", notes = "根据结构类型获取组织树信息 1过滤无人员岗位 2包含无人员岗位 3包含所有岗位 4只包含组织结构 5过滤无人员岗位 无账号人员")
    public Result<TreeNode> fuzzyOrgTree(@ApiParam("搜索关键字") @RequestParam(required = false) String searchKey,
                                         @ApiParam("组织树构建类型") @RequestParam(required = false, defaultValue = "1") Integer buildType) {
        return Result.success(companyHelper.fuzzyOrgTree(searchKey, buildType));
    }

    @GetMapping("/queryByLevel")
    @ApiOperation(value = "按照层级获取子节点", notes = "按照层级获取子节点")
    public Result<LevelNodeInfo> queryByLevel(@ApiParam("父节点OID") @RequestParam(required = false) String oid) {
        return Result.success(companyHelper.queryByLevel(oid));
    }

    @PostMapping("/exportExcel")
    @ApiOperation(response = Result.class, value = "根据公司/部门导出人员excel", notes = "根据公司/部门导出人员excel")
    public Result exportExcel(@ApiParam("导出的跟节点类型") @RequestParam String fromType,
                              @ApiParam("导出的根节点oid") @RequestParam String fromOid,
                              HttpServletResponse response) throws Exception {
        companyHelper.exportExcel(fromType, fromOid, response);
        return Result.success();
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "excel导入", notes = "excel导入")
    public Result<String> importExcel(@ApiParam("导入的组织结构以及人员excel") @RequestParam(value = "file") MultipartFile file) {
        return handleSyncPersonnelResponse(companyHelper.importExcel(file));
    }

    @GetMapping("/refreshADDomainData")
    @ApiOperation(value = "一键导入AD域数据", notes = "一键导入AD域数据")
    public Result<String> refreshADDomainData() {
        return handleSyncPersonnelResponse(companyHelper.refreshADDomainData());
    }

    private Result handleSyncPersonnelResponse(String message) {
        Result result;
        if (StringUtils.isEmpty(message)) {
            result = Result.success();
        } else {
            result = new Result(0, message);
        }
        return result;
    }

}
