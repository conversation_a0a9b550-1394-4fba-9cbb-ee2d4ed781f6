package cn.jwis.platform.iam.organization;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.organization.dto.PositionCreateDTO;
import cn.jwis.platform.iam.organization.dto.PositionUpdateDTO;
import cn.jwis.platform.iam.organization.entity.PositionDef;
import cn.jwis.platform.iam.organization.helper.PositionHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
@RestController
@RequestMapping("/position/config")
@Api(tags = "岗位配置 API", value = "Position Config Controller")
public class PositionConfigController {

    @Resource
    PositionHelper positionHelper;

    @PostMapping("/create")
    @ApiOperation(value = "创建岗位配置", notes = "创建岗位配置")
    public Result<PositionDef> create(@RequestBody PositionCreateDTO dto) {
        return Result.success(positionHelper.create(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新岗位配置", notes = "更新岗位配置")
    public Result<PositionDef> update(@RequestBody PositionUpdateDTO dto) {
        return Result.success(positionHelper.update(dto));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除岗位配置", notes = "无法删除已经分配岗位的岗位配置")
    public Result<Long> delete(@ApiParam("岗位配置OID") @RequestParam String oid) {
        return Result.success(positionHelper.delete(oid, PositionDef.TYPE));
    }

    @GetMapping("/fuzzy")
    @ApiOperation(value = "模糊查询岗位配置", notes = "模糊查询岗位配置")
    public Result<List<PositionDef>> fuzzy(@ApiParam("搜索关键字") @RequestParam(required = false) String searchKey) {
        return Result.success(positionHelper.fuzzy(searchKey));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "模糊分页查询岗位配置", notes = "模糊分页查询岗位配置")
    public Result<PageResult<PositionDef>> fuzzyPage(@RequestBody PageSimpleDTO dto) {
        return Result.success(positionHelper.fuzzyPage(dto));
    }

    @GetMapping("/findByOid")
    @ApiOperation(value = "通过OID查询岗位配置", notes = "通过OID查询岗位配置")
    public Result<PositionDef> findByOid(@ApiParam("已配置岗位OID") @RequestParam String oid) {
        return Result.success(positionHelper.findByOid(oid));
    }

    @GetMapping("/findByName")
    @ApiOperation(value = "通过名称查询岗位配置", notes = "通过名称查询岗位配置")
    public Result<PositionDef> findByName(@ApiParam("岗位配置名称") @RequestParam String name) {
        return Result.success(positionHelper.findByName(name));
    }


    @PostMapping("/exportExcel")
    @ApiOperation(value = "导出岗位excel", notes = "导出岗位excel")
    public Result exportExcel(HttpServletResponse response) throws Exception {
        positionHelper.exportExcel(response);
        return Result.success();
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "导入岗位excel", notes = "导入岗位excel")
    public Result<Boolean> importExcel(@RequestParam(value = "file") MultipartFile file) {
        return Result.success(positionHelper.importExcel(file));
    }
}
