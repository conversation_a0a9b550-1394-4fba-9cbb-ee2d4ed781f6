package cn.jwis.platform.iam.account;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.account.dto.AccountCreateDTO;
import cn.jwis.platform.iam.account.dto.AccountRuleCreateDTO;
import cn.jwis.platform.iam.account.dto.AccountRuleUpdateDTO;
import cn.jwis.platform.iam.account.hepler.IamAccountHelper;
import cn.jwis.platform.iam.accountrule.AccountRule;
import cn.jwis.platform.iam.personnel.response.AccountWithPersonnelInfo;
import cn.jwis.platform.iam.personnel.response.EmptyUser;
import cn.jwis.platform.iam.user.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/account")
@Api(tags = "账号配置", value = "Setting Controller")
public class AccountController {

    @Resource
    IamAccountHelper iamAccountHelper;

    @PostMapping("/createAccountRule")
    @ApiOperation(value = "创建账号生成规则", notes = "创建账号生成规则")
    public Result<AccountRule> create(@RequestBody AccountRuleCreateDTO dto) {
        return Result.success(iamAccountHelper.createAccountRule(dto));
    }

    @PostMapping("/updateAccountRule")
    @ApiOperation(value = "更新账号规则设置", notes = "更新账号规则设置")
    public Result<AccountRule> create(@RequestBody AccountRuleUpdateDTO dto) {
        return Result.success(iamAccountHelper.updateAccountRule(dto));
    }

    @GetMapping("/queryAccountRule")
    @ApiOperation(value = "查询账号生成规则", notes = "查询账号生成规则")
    public Result<AccountRule> queryAccountRule() {
        return Result.success(iamAccountHelper.queryAccountRule());
    }

    @PostMapping("/generateAccount")
    @ApiOperation(value = "生成账号可关联人员", notes = "生成账号可关联人员")
    public Result<User> generateAccount(@RequestBody AccountCreateDTO dto) {
        return Result.success(iamAccountHelper.generateAccount(dto));
    }

    @GetMapping("/getNextAccount")
    @ApiOperation(value = "获取自动生成账号", notes = "获取自动生成账号")
    public Result<String> generateAccount() {
        return Result.success(iamAccountHelper.getNextAccount());
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "账号信息查询", notes = "账号信息查询")
    public Result<PageResult<AccountWithPersonnelInfo>> fuzzyPage(@RequestBody PageSimpleDTO dto) {
        return Result.success(iamAccountHelper.fuzzyPage(dto));
    }

    @PostMapping("/queryByAccount")
    @ApiOperation(value = "账号信息查询", notes = "账号信息查询")
    public Result<List<User>> queryByAccount(@RequestBody List<String> accountList) {
        return Result.success(iamAccountHelper.queryByAccount(accountList));
    }

    @GetMapping("/queryEmptyUser")
    @ApiOperation(value = "查询未分配人员账号", notes = "查询未分配人员账号")
    public Result<List<EmptyUser>> queryEmptyUser() {
        return Result.success(iamAccountHelper.queryEmptyUser());
    }
}
