package cn.jwis.platform.iam.permission;

import cn.jwis.framework.base.response.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2022/11/18
 * @Description :
 */

@RestController
@RequestMapping("/permission/v1")
public class PermissionController {



    /**
     * 默认方法，前端脚手架适配使用
     *
     * @return
     */
    @PostMapping("/role/findTernaryUserState")
    public Result findTernaryUserState() {
        return Result.success(false);
    }

}
