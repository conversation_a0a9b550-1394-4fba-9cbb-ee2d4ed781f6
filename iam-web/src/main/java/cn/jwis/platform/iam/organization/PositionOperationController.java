package cn.jwis.platform.iam.organization;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.organization.dto.PositionDeleteDTO;
import cn.jwis.platform.iam.organization.dto.PositionSetUpDTO;
import cn.jwis.platform.iam.organization.dto.PositionUpdateSetupDTO;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.organization.helper.PositionHelper;
import cn.jwis.platform.iam.personnel.response.PositionWithRel;
import cn.jwis.platform.iam.response.SetUpWithVacancy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
@RestController
@RequestMapping("/position/opt")
@Api(tags = "岗位操作 API", value = "Position Operation Controller")
public class PositionOperationController {

    @Resource
    PositionHelper positionHelper;

    @PostMapping("/setUp/create")
    @ApiOperation(value = "为部门/公司设定岗位", notes = "为部门/公司设定岗位")
    public Result<List<Position>> createSetUp(@RequestBody PositionSetUpDTO dto) {
        return Result.success(positionHelper.createSetUp(dto));
    }

    @PostMapping("/setUp/update")
    @ApiOperation(value = "更新部门/公司的岗位的相关数据", notes = "更新部门/公司的岗位的相关数据")
    public Result<SetUpWithVacancy> updateSetUp(@RequestBody PositionUpdateSetupDTO dto) {
        return Result.success(positionHelper.updateSetUp(dto));
    }

    @PostMapping("/setUp/delete")
    @ApiOperation(value = "删除部门/公司的岗位", notes = "删除部门/公司的岗位无法删除已分配人员岗位")
    public Result<Long> deleteSetUp(@RequestBody PositionDeleteDTO dto) {
        return Result.success(positionHelper.deleteSetUp(dto));
    }

    @GetMapping("/setUp/fuzzyByFrom")
    @ApiOperation(value = "查询部门/公司的岗位", notes = "查询部门/公司的岗位")
    public Result<PageResult<PositionWithRel>> fuzzyByFrom(@ApiParam("所属组织类型Company或Department") @RequestParam String fromType,
                                                           @ApiParam("所属组织OID") @RequestParam String fromOid,
                                                           @ApiParam("搜索关键字非必填") @RequestParam(required = false) String searchKey,
                                                           @ApiParam("页码") @RequestParam(defaultValue = "1") int index,
                                                           @ApiParam("分页大小") @RequestParam(defaultValue = "10") int size) {
        return Result.success(positionHelper.fuzzyByFrom(fromType, fromOid, searchKey, index, size));
    }

}
