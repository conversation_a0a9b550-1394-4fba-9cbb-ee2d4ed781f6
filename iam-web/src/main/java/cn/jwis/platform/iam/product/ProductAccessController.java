package cn.jwis.platform.iam.product;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.personnel.response.PersonnelWithAccessInfo;
import cn.jwis.platform.iam.personnel.response.ProductWithAccessInfo;
import cn.jwis.platform.iam.product.dto.AccessDTO;
import cn.jwis.platform.iam.product.dto.AccessMultipleProductDTO;
import cn.jwis.platform.iam.product.dto.AccessUpdateDTO;
import cn.jwis.platform.iam.product.dto.FuzzyByProductOidDTO;
import cn.jwis.platform.iam.product.dto.ProductPageByPersonnelDTO;
import cn.jwis.platform.iam.product.helper.ProductHelper;
import cn.jwis.platform.iam.product.response.ProductMemberNum;
import cn.jwis.platform.iam.structure.TreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： Created in 2022/11/2
 * @description : 应用相关管理
 */
@RestController
@RequestMapping("/product/access")
@Api(tags = "应用API", value = "Product Access Controller")
public class ProductAccessController {

    @Resource
    ProductHelper productHelper;

    @Resource
    ProductService productService;

    @PostMapping("/create")
    @ApiOperation(value = "授权人员", notes = "授权人员")
    public Result<Boolean> create(@RequestBody AccessDTO dto) {
        return Result.success(productHelper.toAccess(dto));
    }

    @PostMapping("/createMultiple")
    @ApiOperation(value = "人员一次授权多个应用", notes = "人员一次授权多个应用")
    public Result<Boolean> create(@RequestBody AccessMultipleProductDTO dto) {
        return Result.success(productHelper.createMultiple(dto));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除授权", notes = "删除授权")
    public Result<Boolean> delete(@RequestBody AccessDTO dto) {
        return Result.success(productHelper.cancelAccess(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新应用授权", notes = "更新应用授权")
    public Result<Boolean> update(@RequestBody AccessUpdateDTO dto) {
        return Result.success(productHelper.updateAccess(dto));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "查询授权人员", notes = "查询授权人员")
    public Result<PageResult<PersonnelWithAccessInfo>> fuzzyPage(@RequestBody FuzzyByProductOidDTO dto) {
        return Result.success(productHelper.fuzzyAccessPage(dto));
    }

    @PostMapping("/queryAccessProductByPersonnel")
    @ApiOperation(value = "查询人员授权应用信息", notes = "查询人员授权应用信息")
    public Result queryAccessProductByPersonnel(@RequestBody ProductPageByPersonnelDTO dto) {
        return Result.success(productHelper.queryAccessProductByPersonnel(dto));
    }

    @PostMapping("/queryNoAccessProductByPersonnel")
    @ApiOperation(value = "查询人员未授权应用清单", notes = "查询人员未授权应用清单")
    public Result<PageResult<ProductWithAccessInfo>> queryNoAccessProductByPersonnel(@RequestBody ProductPageByPersonnelDTO dto) {
        return Result.success(productHelper.queryNoAccessProductByPersonnel(dto));
    }

    @GetMapping("/queryPersonnelTreeWithAccessFlag")
    @ApiOperation(value = "查询授权人员", notes = "查询授权人员")
    public Result<TreeNode> queryPersonnelTreeWithAccessFlag(@ApiParam("应用OID") @RequestParam String productOid,
                                                             @ApiParam("搜索关键字非必填")  @RequestParam(required = false) String searchKey) {
        return Result.success(productHelper.queryPersonnelTreeWithAccessFlag(productOid, searchKey));
    }
}
