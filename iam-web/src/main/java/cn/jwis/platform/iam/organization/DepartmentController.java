package cn.jwis.platform.iam.organization;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.organization.dto.DepartmentCreateDTO;
import cn.jwis.platform.iam.organization.dto.DepartmentUpdateDTO;
import cn.jwis.platform.iam.organization.dto.FuzzyDeptPageByOrgDTO;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.helper.DepartmentHelper;
import cn.jwis.platform.iam.organization.response.DepartmentWithSubscribe;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 */
@RestController
@RequestMapping("/department")
@Api(tags = "部门 API", value = "Department Controller")
public class DepartmentController {

    @Resource
    DepartmentHelper departmentHelper;

    @PostMapping("/create")
    @ApiOperation(value = "创建部门", notes = "创建部门")
    public Result<Department> create(@RequestBody DepartmentCreateDTO dto) {
        return Result.success(departmentHelper.create(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新部门", notes = "更新部门")
    public Result<Department> update(@RequestBody DepartmentUpdateDTO dto) {
        return Result.success(departmentHelper.update(dto));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除部门", notes = "删除部门")
    public Result<Long> delete(@ApiParam("部门OID") @RequestParam String oid) {
        return Result.success(departmentHelper.delete(oid, Department.TYPE));
    }

    @GetMapping("/fuzzy")
    @ApiOperation(value = "模糊查询部门", notes = "模糊查询部门")
    public Result<List<Department>> fuzzy(@ApiParam("搜索关键字") @RequestParam(required = false) String searchKey) {
        return Result.success(departmentHelper.fuzzy(searchKey));
    }

    @PostMapping("/fuzzyPage")
    @ApiOperation(value = "模糊分页查询部门", notes = "模糊分页查询部门")
    public Result<PageResult<Department>> fuzzyPage(@RequestBody PageSimpleDTO dto) {
        return Result.success(departmentHelper.fuzzyPage(dto));
    }

    @PostMapping("/fuzzyPageWithSubscribe")
    @ApiOperation(value = "模糊分页查询订阅部门", notes = "模糊分页查询订阅部门")
    public Result<PageResult<DepartmentWithSubscribe>> fuzzyPageWithSubscribe(@RequestBody FuzzyDeptPageByOrgDTO dto) {
        return Result.success(departmentHelper.fuzzyPageWithSubscribe(dto));
    }

    @GetMapping("/findByOid")
    @ApiOperation(value = "通过OID查询部门", notes = "通过OID查询部门")
    public Result<Department> findByOid(@ApiParam("部门OID") @RequestParam String oid) {
        return Result.success(departmentHelper.findByOid(oid));
    }
}
