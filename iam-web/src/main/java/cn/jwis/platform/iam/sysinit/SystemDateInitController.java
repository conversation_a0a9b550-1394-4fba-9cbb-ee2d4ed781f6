package cn.jwis.platform.iam.sysinit;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2022/12/23
 * @Description :
 */

@IgnoreRestUrlAccess
@RestController
@RequestMapping("/sys/init")
@Api(tags = "系统数据初始化", value = "System Init")
public class SystemDateInitController {

    @Resource
    SystemDataInitHelper systemDataInitHelper;

    @GetMapping("/createVeDefAndSite")
    @ApiOperation(response = Result.class, value = "初始化VertexDef和Site对象 第二步", notes = "初始化VertexDef和Site对象 第二步")
    public Result createVeDefAndSite() {
        return Result.success(systemDataInitHelper.createVeDefAndSite());
    }

    @PostMapping("/createProperty")
    @ApiOperation(response = Result.class, value = "初始化Property 第三步", notes = "初始化Property 第三步")
    public Result createProperty(@RequestParam(value = "file") MultipartFile file) {
        return Result.success(systemDataInitHelper.createProperty(file));
    }

    @PostMapping("/createLayout")
    @ApiOperation(response = Result.class, value = "初始化Layout 第四步", notes = "初始化Layout 第四步")
    public Result createLayout(@RequestParam(value = "file") MultipartFile file) {
        return Result.success(systemDataInitHelper.createLayout(file));
    }


    @GetMapping("/createPasswordPolicy")
    @ApiOperation(response = Result.class, value = "创建密级 第一步", notes = "创建密级 第一步")
    public Result createPasswordPolicy() {
        return Result.success(systemDataInitHelper.createPasswordPolicy());
    }
}
