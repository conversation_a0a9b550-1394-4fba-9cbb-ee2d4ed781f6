package cn.jwis.platform.iam.auth;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.auth.dto.AuthDTO;
import cn.jwis.platform.iam.auth.dto.LoginDTO;
import cn.jwis.platform.iam.auth.dto.TokenDTO;
import cn.jwis.platform.iam.auth.helper.AuthHelper;
import cn.jwis.platform.iam.auth.helper.DeviceHelper;
import cn.jwis.platform.iam.common.util.IPUtil;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping(value = "/authentication")
@Api(tags = {"认证服务"}, value = "AuthController")
public class AuthController {

    @Autowired
    private AuthHelper authHelper;

    @Autowired
    private DeviceHelper deviceHelper;

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ApiOperation(value = "User login", notes = "User login via account and password")
    @IgnoreRestUrlAccess
    public Result<AuthDTO> login(HttpServletRequest request, @RequestBody LoginDTO loginDTO) {
        try {
            String ip = IPUtil.getIpAddr(request);
            String appName = request.getHeader("appName");
            AuthDTO authDTO = authHelper.login(appName, loginDTO, ip);
            if (authDTO.getCode() != 0) {
                return Result.Fail(authDTO.getCode(), authDTO.getTips());
            }
            deviceHelper.addDevice(loginDTO.getDevice(), authDTO.getUser());
            return Result.success("success", authDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.Fail(-1, e.getMessage());
        }
    }


    @RequestMapping(value = "/unlockAccount", method = RequestMethod.POST)
    @ApiOperation(value = "unlockAccount", notes = "unlockAccount")
    public Result unlockAccount(@RequestParam("account") String account) {
        authHelper.unlockAccount(account);
        return Result.success();
    }


    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    @ApiOperation(value = "User logout API", notes = "User logout API")
    public Result logout(@ApiParam("账户信息") @RequestParam("account") String account,
                         @ApiParam("是否移动端默认为false") @RequestParam(value = "mobile", required = false) boolean isMobile) {
        AuthDTO dto = authHelper.logout(account, isMobile);
        Result result = new Result(dto.getCode(), dto.getTips());
        return result;
    }

    @RequestMapping(value = "/access-token", method = RequestMethod.GET)
    @ApiOperation(value = "check JsonWebToken API", notes = "User logout API")
    public Result<TokenDTO> checkJsonWebToken(HttpServletRequest request) {
        try {
            String token = request.getHeader("accesstoken");
            return Result.success(authHelper.accessToken(token));
        } catch (Exception e) {
            return Result.Fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/user/tenantInfo", method = RequestMethod.GET)
    @ApiOperation(value = "Empty API", notes = "Empty API")
    public Result<JSONObject> tenantInfo(HttpServletRequest request) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tenantList", Lists.newArrayList().add("iamDefault"));
            jsonObject.put("userTenantAuthInfo", "{}");
            return Result.success(jsonObject);
        } catch (Exception e) {
            return Result.Fail(e.getMessage());
        }
    }
}
