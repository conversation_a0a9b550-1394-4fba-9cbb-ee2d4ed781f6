<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.jwis.platform.iam</groupId>
    <artifactId>iam-server</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>
    <modules>
        <module>iam-helper</module>
        <module>iam-remote</module>
        <module>iam-remote-service</module>
        <module>iam-web</module>
        <module>iam-launcher</module>
    </modules>

    <name>iam-server</name>
    <description>IAM系统服务</description>

    <parent>
        <groupId>cn.jwis.platform.iam</groupId>
        <artifactId>jwi-platform-iam-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <properties>
        <namespace></namespace>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>


        <jjwt.version>0.11.5</jjwt.version>

        <jwi.platform.plm.file.version>3.2.0</jwi.platform.plm.file.version>
        <jwi.platform.plm.foundation.version>3.2.0</jwi.platform.plm.foundation.version>

        <jwi.framework.msgqueue.client.version>2.2.0</jwi.framework.msgqueue.client.version>

        <jwi.platform.iam.server.version>1.0.0</jwi.platform.iam.server.version>
        <jwi.platform.iam.config.version>1.0.0</jwi.platform.iam.config.version>


    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- iam-server-->
            <dependency>
                <groupId>cn.jwis.platform.iam</groupId>
                <artifactId>iam-helper</artifactId>
                <version>${jwi.platform.iam.server.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.iam</groupId>
                <artifactId>iam-web</artifactId>
                <version>${jwi.platform.iam.server.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.iam</groupId>
                <artifactId>iam-remote</artifactId>
                <version>${jwi.platform.iam.server.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.iam</groupId>
                <artifactId>iam-remote-service</artifactId>
                <version>${jwi.platform.iam.server.version}</version>
            </dependency>


            <dependency>
                <groupId>cn.jwis.platform.iam</groupId>
                <artifactId>gateway-access</artifactId>
                <version>${jwi.platform.iam.server.version}</version>
            </dependency>

            <!-- 外部依赖-->
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-helper</artifactId>
                <version>${jwi.platform.plm.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-repo-neo4j</artifactId>
                <version>${jwi.platform.plm.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-web</artifactId>
                <version>${jwi.platform.plm.foundation.version}</version>
            </dependency>


            <!--data distribution  数据下发SDK-->
            <dependency>
                <groupId>cn.jwis.framework</groupId>
                <artifactId>msgqueue-client-sdk</artifactId>
                <version>${jwi.framework.msgqueue.client.version}</version>
            </dependency>

            <!-- MINIO 分布式文件系统-->
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>file-repo-neo4j</artifactId>
                <version>${jwi.platform.plm.file.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>file-web</artifactId>
                <version>${jwi.platform.plm.file.version}</version>
            </dependency>



            <!--  iam config 配置服务-->

            <dependency>
                <groupId>cn.jwis.platform.iam</groupId>
                <artifactId>config-web</artifactId>
                <version>${jwi.platform.iam.config.version}</version>
            </dependency>






            <!--    JWI 依赖 -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--        配置中心-->
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>config-center-sdk</artifactId>
        </dependency>

        <!--配置管理-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <!-- spring boot集成 -->
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>jwis-base-service</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>audit-framework-repo-neo4j</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>foundation-helper</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>permission-helper</artifactId>
                    <groupId>cn.jwis.platform.plm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>