<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="iam-helper" />
        <module name="iam-web" />
        <module name="iam-remote-service" />
        <module name="iam-launcher" />
        <module name="iam-remote" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="iam-entity" target="1.8" />
      <module name="iam-repo" target="1.8" />
      <module name="iam-repo-dm" target="1.8" />
      <module name="iam-repo-mysql" target="1.8" />
      <module name="iam-repo-neo4j" target="1.8" />
      <module name="iam-repo-oracle" target="1.8" />
      <module name="iam-sdk" target="1.8" />
      <module name="iam-service" target="1.8" />
      <module name="untitled" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="iam-entity" options="-parameters -parameters" />
      <module name="iam-helper" options="-parameters -parameters" />
      <module name="iam-launcher" options="-parameters -parameters" />
      <module name="iam-remote" options="-parameters -parameters" />
      <module name="iam-remote-service" options="-parameters -parameters" />
      <module name="iam-repo" options="-parameters -parameters" />
      <module name="iam-repo-dm" options="-parameters -parameters" />
      <module name="iam-repo-mysql" options="-parameters -parameters" />
      <module name="iam-repo-neo4j" options="-parameters -parameters" />
      <module name="iam-repo-oracle" options="-parameters -parameters" />
      <module name="iam-service" options="-parameters -parameters" />
      <module name="iam-web" options="-parameters -parameters" />
      <module name="untitled" options="-parameters -parameters" />
    </option>
  </component>
</project>