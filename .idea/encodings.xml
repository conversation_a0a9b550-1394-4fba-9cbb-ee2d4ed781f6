<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" native2AsciiForPropertiesFiles="true" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/iam-helper/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-helper/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-launcher/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-launcher/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-remote-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-remote-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-remote/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-remote/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/iam-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>