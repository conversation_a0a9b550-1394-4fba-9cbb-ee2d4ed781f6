<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="maven-public" />
      <option name="name" value="maven-public" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/sells-maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/sells-maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="rdc-releases" />
      <option name="name" value="rdc-releases" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/maven-releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="maven-releases" />
      <option name="name" value="maven-releases" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/maven-releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="maven-releases" />
      <option name="name" value="maven-releases" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/sells-maven-releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="snapshots" />
      <option name="name" value="snapshots" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="rdc-snapshots" />
      <option name="name" value="rdc-snapshots" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/maven-snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sells-maven-public" />
      <option name="name" value="sells-maven-public" />
      <option name="url" value="http://jwi-in-nexus.jwis.cn/repository/sells-maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
  </component>
</project>